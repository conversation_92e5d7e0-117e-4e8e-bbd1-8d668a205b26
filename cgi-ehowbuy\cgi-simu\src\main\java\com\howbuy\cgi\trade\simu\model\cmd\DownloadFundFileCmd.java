package com.howbuy.cgi.trade.simu.model.cmd;

import com.howbuy.cgi.common.validate.ValidationAnnotation;
import com.howbuy.validator.util.ValidatorTypeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description 产品下载入参
 * @Author: yun.lu
 * Date: 2025/6/17 18:22
 */
@Data
public class DownloadFundFileCmd implements Serializable {
    /**
     * 产品编码
     */
    @ValidationAnnotation(validatorType = ValidatorTypeEnum.String, fieldName = "产品编码", isRequired = true)
    private String fundCode;
    /**
     * 合同类型code
     */
    @ValidationAnnotation(validatorType = ValidatorTypeEnum.String, fieldName = "合同类型code", isRequired = true)
    private String agreementCode;
}
