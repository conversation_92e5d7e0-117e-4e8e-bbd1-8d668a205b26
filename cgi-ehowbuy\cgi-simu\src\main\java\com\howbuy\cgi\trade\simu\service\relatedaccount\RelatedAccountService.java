/**
 * Copyright (c) 2021, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cgi.trade.simu.service.relatedaccount;

import com.alibaba.fastjson.JSON;
import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.acccenter.common.enums.RelatedAccountTypeEnum;
import com.howbuy.acccenter.facade.query.querycustbasicinfo.QueryCustBasicInfoFacade;
import com.howbuy.acccenter.facade.query.querycustbasicinfo.QueryCustBasicInfoRequest;
import com.howbuy.acccenter.facade.query.querycustbasicinfo.QueryCustBasicInfoResponse;
import com.howbuy.acccenter.facade.query.queryhboneinfo.QueryAccHboneInfoFacade;
import com.howbuy.acccenter.facade.query.queryhboneinfo.QueryAccHboneInfoRequest;
import com.howbuy.acccenter.facade.query.queryhboneinfo.QueryAccHboneInfoResponse;
import com.howbuy.acccenter.facade.query.queryrelatedaccount.*;
import com.howbuy.acccenter.facade.query.queryrelatedaccount.bean.RelatedAccountBean;
import com.howbuy.acccenter.facade.query.queryrelatedaccount.bean.SubRelatedAccountBean;
import com.howbuy.acccenter.facade.query.querytxacctinfofacade.QueryTxAcctInfoFacade;
import com.howbuy.acccenter.facade.query.querytxacctinfofacade.QueryTxAcctInfoRequest;
import com.howbuy.acccenter.facade.query.querytxacctinfofacade.QueryTxAcctInfoResponse;
import com.howbuy.acccenter.facade.trade.relatedaccount.*;
import com.howbuy.cgi.common.enums.BizErrorEnum;
import com.howbuy.cgi.common.exception.BizException;
import com.howbuy.cgi.common.util.RemoteUtil;
import com.howbuy.crm.nt.conscust.dto.ConscustInfoDomain;
import com.howbuy.crm.nt.conscust.request.QueryConscustListRequest;
import com.howbuy.crm.nt.conscust.response.QueryConscustListResponse;
import com.howbuy.crm.nt.conscust.service.QueryConscustListService;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 关联账户服务
 *
 * <AUTHOR>
 * @date 2021/7/12 13:59
 * @since JDK 1.8
 */
@Service("relatedAccountService")
public class RelatedAccountService {

    private static final Logger logger = LogManager.getLogger();

    @Autowired
    @Qualifier("simu.queryConscustListService")
    private QueryConscustListService queryConscustListService;
    @Autowired
    @Qualifier("simu.queryTxAcctInfoFacade")
    private QueryTxAcctInfoFacade queryTxAcctInfoFacade;
    @Autowired
    @Qualifier("simu.queryAccHboneInfoFacade")
    private QueryAccHboneInfoFacade queryAccHboneInfoFacade;
    @Autowired
    @Qualifier("simu.queryCustBasicInfoFacade")
    private QueryCustBasicInfoFacade queryCustBasicInfoFacade;
    @Autowired
    @Qualifier("simu.queryRelatedAccountFacade")
    private QueryRelatedAccountFacade queryRelatedAccountFacade;
    @Autowired
    @Qualifier("simu.querySubRelatedAccountFacade")
    private QuerySubRelatedAccountFacade querySubRelatedAccountFacade;
    @Autowired
    @Qualifier("simu.closeRelatedAccountFacade")
    private CloseRelatedAccountFacade closeRelatedAccountFacade;
    @Autowired
    @Qualifier("simu.closeSubRelatedAccountFacade")
    private CloseSubRelatedAccountFacade closeSubRelatedAccountFacade;
    private static final String FAIL_ERROR_CODE="5220025";
    private static final String H5_FAIL_ERROR_CODE="B5220025";

    /**
     * 判断是否展示关联账户入口
     *
     * @param hboneNo
     * @return java.lang.String
     * @author: huaqiang.liu
     * @date: 2021/7/12 14:09
     * @since JDK 1.8
     */
    public String showRelatedAccount(String hboneNo, String txAcctNo) {
        // 已开通交易账户且是好买分销
        if (StringUtils.isBlank(txAcctNo)) {
            logger.info("RelatedAccountService|showRelatedAccount|txAcctNo is null.");
            return YesOrNoEnum.NO.getCode();
        }
        QueryTxAcctInfoRequest txReq = new QueryTxAcctInfoRequest();
        txReq.setTxAcctNo(txAcctNo);
        txReq.setDisCode(DisCodeEnum.HM.getCode());
        QueryTxAcctInfoResponse txRes = queryTxAcctInfoFacade.execute(txReq);
        if (txRes == null || StringUtils.isBlank(txRes.getDisTxAcctNo())) {
            logger.info("RelatedAccountService|showRelatedAccount|queryTxAcctInfo fail.");
            return YesOrNoEnum.NO.getCode();
        }

        // 客户在CRM当前所属投顾非虚拟投顾，且投顾所属组织架构，在IC/HBC下
        ConscustInfoDomain conscustInfo = queryConscustInfo(hboneNo);
        if (conscustInfo == null) {
            return YesOrNoEnum.NO.getCode();
        }
        String ishighorg = conscustInfo.getIshighorg();
        if (!YesOrNoEnum.YES.getCode().equals(ishighorg)) {
            return YesOrNoEnum.NO.getCode();
        }
        return YesOrNoEnum.YES.getCode();
    }

    /**
     * 查询投顾客户信息
     *
     * @param hboneNo
     * @return com.howbuy.crm.nt.conscust.dto.ConscustInfoDomain
     * @author: huaqiang.liu
     * @date: 2021/7/15 20:23
     * @since JDK 1.8
     */
    public ConscustInfoDomain queryConscustInfo(String hboneNo) {
        QueryConscustListRequest consReq = new QueryConscustListRequest();
        consReq.setHboneno(hboneNo);
        QueryConscustListResponse consRes = queryConscustListService.queryConscustInfo(consReq);
        if (consRes == null || CollectionUtils.isEmpty(consRes.getConscustlist())) {
            logger.info("RelatedAccountService|showRelatedAccount|queryConscustInfo fail.");
            return null;
        }
        return consRes.getConscustlist().get(0);
    }

    /**
     * 根据手机号查询一账通
     *
     * @param mobile
     * @return java.lang.String
     * @author: huaqiang.liu
     * @date: 2021/7/15 18:15
     * @since JDK 1.8
     */
    public String queryHboneNoByMobile(String mobile) {
        QueryAccHboneInfoRequest request = new QueryAccHboneInfoRequest();
        request.setMobileDigest(DigestUtil.digest(mobile));
        QueryAccHboneInfoResponse response = queryAccHboneInfoFacade.execute(request);
        if (response == null || !RemoteUtil.isSuccess(response.getReturnCode())) {
            logger.error("queryHboneNoByMobile fail. mobile:{} res:{}", mobile, JSON.toJSONString(response));
            return null;
        }
        return response.getHboneNo();
    }

    /**
     * 查询客户基本信息
     *
     * @param hboneNo
     * @return com.howbuy.acccenter.facade.query.querycustbasicinfo.QueryCustBasicInfoResponse
     * @author: huaqiang.liu
     * @date: 2021/7/13 17:24
     * @since JDK 1.8
     */
    public QueryCustBasicInfoResponse queryCustBasicInfo(String hboneNo) {
        QueryCustBasicInfoRequest request = new QueryCustBasicInfoRequest();
        request.setHboneNo(hboneNo);
        request.setDisCode(DisCodeEnum.HM.getCode());
        QueryCustBasicInfoResponse response = queryCustBasicInfoFacade.execute(request);
        if (response != null && FAIL_ERROR_CODE.equals(response.getReturnCode())) {
            logger.error("查询用户基础信息失败,hboneNo={},response={}", hboneNo, JSON.toJSONString(response));
            throw new BizException(H5_FAIL_ERROR_CODE, "分销交易账户信息不存在");
        }
        if (response == null || !RemoteUtil.isSuccess(response.getReturnCode())) {
            logger.error("queryCustBasicInfo fail. hboneNo:{} res:{}", hboneNo, JSON.toJSONString(response));
            throw new BizException(BizErrorEnum.SYSTEM_ERROR.getCode(), BizErrorEnum.SYSTEM_ERROR.getDesc());
        }
        return response;
    }

    /**
     * 查询关联账户信息（不知道一账通号是否主账户）
     *
     * @param hboneNo
     * @param accountType
     * @return com.howbuy.acccenter.facade.query.queryrelatedaccount.bean.RelatedAccountBean
     * @author: huaqiang.liu
     * @date: 2021/7/13 16:05
     * @since JDK 1.8
     */
    public RelatedAccountBean queryRelatedAccount(String hboneNo, RelatedAccountTypeEnum accountType) {
        // 假设当前客户为主账户
        RelatedAccountBean account = queryRelatedAccountWithType(hboneNo, null, accountType);
        // 不是主账户
        if (account == null) {
            // 查询副账户信息
            SubRelatedAccountBean bean = querySubRelatedAccount(hboneNo, accountType);
            if (bean != null) {
                account = queryRelatedAccountWithType(null, bean.getRelatedAccountId(), accountType);
            }
        }
        return account;
    }

    /**
     * 查询副账户
     *
     * @param hboneNo
     * @param accountType
     * @return com.howbuy.acccenter.facade.query.queryrelatedaccount.bean.SubRelatedAccountBean
     * @author: huaqiang.liu
     * @date: 2021/7/13 15:51
     * @since JDK 1.8
     */
    public SubRelatedAccountBean querySubRelatedAccount(String hboneNo, RelatedAccountTypeEnum accountType) {
        QuerySubRelatedAccountRequest request = new QuerySubRelatedAccountRequest();
        request.setSubHboneNo(hboneNo);
        request.setRelatedAccountType(accountType);
        QuerySubRelatedAccountResponse response = querySubRelatedAccountFacade.execute(request);
        if (response == null || !RemoteUtil.isSuccess(response.getReturnCode())) {
            logger.error("querySubRelatedAccount fail. hboneNo:{} accountType:{} res:{}", hboneNo, accountType, JSON.toJSONString(response));
            throw new BizException(BizErrorEnum.SYSTEM_ERROR.getCode(), BizErrorEnum.SYSTEM_ERROR.getDesc());
        }

        List<SubRelatedAccountBean> subAccountList = response.getSubRelatedAccountBeanList();
        if (subAccountList != null && !subAccountList.isEmpty()) {
            return subAccountList.get(0);
        } else {
            return null;
        }
    }


    public List<RelatedAccountBean> queryRelatedAccount(String masterHboneNo, String accountId, RelatedAccountTypeEnum accountType) {
        QueryRelatedAccountRequest request = new QueryRelatedAccountRequest();
        request.setRelatedAccountId(accountId);
        request.setHboneNo(masterHboneNo);
        request.setRelatedAccountType(accountType);
        QueryRelatedAccountResponse response = queryRelatedAccountFacade.execute(request);
        if (response == null || !RemoteUtil.isSuccess(response.getReturnCode())) {
            logger.error("queryFamilyInfo fail. accountId:{} masterHboneNo:{} res:{}", accountId, masterHboneNo, JSON.toJSONString(response));
            throw new BizException(BizErrorEnum.SYSTEM_ERROR.getCode(), BizErrorEnum.SYSTEM_ERROR.getDesc());
        }

        List<RelatedAccountBean> accountList = response.getRelatedAccountBeanList();
        if (accountList != null && !accountList.isEmpty()) {
            return accountList;
        } else {
            return null;
        }
    }

    /**
     * 查询主账户
     *
     * @param masterHboneNo
     * @param accountId
     * @param accountType
     * @return java.util.List<com.howbuy.acccenter.facade.query.queryrelatedaccount.bean.RelatedAccountBean>
     * @author: huaqiang.liu
     * @date: 2021/7/13 15:56
     * @since JDK 1.8
     */
    public RelatedAccountBean queryRelatedAccountWithType(String masterHboneNo, String accountId, RelatedAccountTypeEnum accountType) {
        if (accountType == null) {
            throw new RuntimeException("此接口必须指定accountType");
        }
        List<RelatedAccountBean> accountList = queryRelatedAccount(masterHboneNo, accountId, accountType);
        if (accountList != null && !accountList.isEmpty()) {
            return accountList.get(0);
        } else {
            return null;
        }
    }

    /**
     * 查询家庭主账户
     *
     * @param masterHboneNo
     * @param accountId
     * @return com.howbuy.acccenter.facade.query.queryrelatedaccount.bean.RelatedAccountBean
     * @author: huaqiang.liu
     * @date: 2021/7/20 21:36
     * @since JDK 1.8
     */
    public RelatedAccountBean queryFamilyRelatedAccount(String masterHboneNo, String accountId) {
        List<RelatedAccountBean> accountList = queryRelatedAccount(masterHboneNo, accountId, RelatedAccountTypeEnum.FAMILY_0);
        if (accountList != null && !accountList.isEmpty()) {
            return accountList.get(0);
        } else {
            return null;
        }
    }

    /**
     * 解除关联账户
     *
     * @param hboneNo
     * @param accountId
     * @return void
     * @author: huaqiang.liu
     * @date: 2021/7/19 9:39
     * @since JDK 1.8
     */
    public List<SubRelatedAccountBean> closeRelatedAccount(String hboneNo, String accountId) {
        CloseRelatedAccountRequest request = new CloseRelatedAccountRequest();
        request.setHboneNo(hboneNo);
        request.setRelatedAccountId(accountId);
        CloseRelatedAccountResponse response = closeRelatedAccountFacade.execute(request);
        if (response == null || !RemoteUtil.isSuccess(response.getReturnCode())) {
            logger.error("closeRelatedAccount fail. hboneNo:{} accountId:{} res:{}", hboneNo, accountId, JSON.toJSONString(response));
            throw new BizException(BizErrorEnum.SYSTEM_ERROR.getCode(), BizErrorEnum.SYSTEM_ERROR.getDesc());
        }
        return response.getSubRelatedAccountList();
    }

    /**
     * 解除子关联账户
     *
     * @param accountId
     * @param subAccountId
     * @return void
     * @author: huaqiang.liu
     * @date: 2021/7/19 9:41
     * @since JDK 1.8
     */
    public void closeSubRelatedAccount(String accountId, String subAccountId) {
        CloseSubRelatedAccountRequest request = new CloseSubRelatedAccountBySubRequest();
        request.setRelatedAccountId(accountId);
        request.setSubRelatedAccountId(subAccountId);
        CloseSubRelatedAccountResponse response = closeSubRelatedAccountFacade.execute(request);
        if (response == null || !RemoteUtil.isSuccess(response.getReturnCode())) {
            logger.error("closeSubRelatedAccount fail. accountId:{} subAccountId:{} res:{}", accountId, subAccountId, JSON.toJSONString(response));
            throw new BizException(BizErrorEnum.SYSTEM_ERROR.getCode(), BizErrorEnum.SYSTEM_ERROR.getDesc());
        }
    }

}