package com.howbuy.cgi.trade.simu.controller.query;

import com.alibaba.fastjson.JSON;
import com.howbuy.cgi.common.CGIConstants;
import com.howbuy.cgi.common.enums.BizErrorEnum;
import com.howbuy.cgi.common.exception.BizException;
import com.howbuy.cgi.common.validate.ValidateUtil;
import com.howbuy.cgi.trade.ccms.simu.SimuCcmsServiceRegister;
import com.howbuy.cgi.trade.simu.common.CGISimuConstants;
import com.howbuy.cgi.trade.simu.common.enums.FilePathStoreBusinessCodeConfig;
import com.howbuy.cgi.trade.simu.controller.AbstractSimuCGIController;
import com.howbuy.cgi.trade.simu.model.cmd.ContractDownCmd;
import com.howbuy.cgi.trade.simu.model.cmd.DownloadFundFileCmd;
import com.howbuy.cgi.trade.simu.model.dto.QueryDownUrlDto;
import com.howbuy.cgi.trade.simu.service.FundService;
import com.howbuy.cgi.trade.simu.util.ControctFileUtil;
import com.howbuy.cgi.trade.simu.util.FileDownUtil;
import com.howbuy.cgi.trade.simu.util.FileSdkPathInfo;
import com.howbuy.cgi.trade.simu.util.FileSdkUtil;
import com.howbuy.interlayer.product.enums.parameter.AgreementCodeEnum;
import com.howbuy.interlayer.product.model.HighProductBaseInfoModel;
import com.howbuy.tms.common.enums.busi.HighProductAgreementTemplateEnum;
import com.howbuy.tms.high.batch.facade.common.Constant;
import com.howbuy.tms.high.orders.facade.search.queryesignaturefilepath.QueryEsignatureFilePathFacade;
import com.howbuy.tms.high.orders.facade.search.queryesignaturefilepath.QueryEsignatureFilePathRequest;
import com.howbuy.tms.high.orders.facade.search.queryesignaturefilepath.QueryEsignatureFilePathResponse;
import com.howbuy.trade.account.service.hbone.HboneService;
import net.sf.oval.exception.ConstraintsViolatedException;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;

@Controller
public class FileDownloadController extends AbstractSimuCGIController {

    @Autowired
    private SimuCcmsServiceRegister simuCcmsServiceRegister;
    @Autowired
    private HboneService hboneService;
    @Autowired
    private FundService fundService;
    @Autowired
    @Qualifier("simu.queryEsignatureFilePathFacade")
    private QueryEsignatureFilePathFacade queryEsignatureFilePathFacade;

    /**
     * @api {POST} /simu/query/filedownload.htm 下载合同
     * @apiVersion 1.0.0
     * @apiGroup SIMU-CGI
     * @apiName /simu/query/filedownload.htm
     * @apiDescription 查询合同列表
     * @apiParam (请求参数) {String} fileType 文件类型
     * @apiParam (请求参数) {String} fundCode 产品代码
     * @apiParam (请求参数) {String} fundManCode TA代码
     * @apiParam (请求参数) {String} contractNo 合同号
     * @apiParam (请求参数) {String} appSheetSerialNo 合同号
     * @apiParam (请求参数) {String} number 补充协议序号
     * @apiParam (请求参数) {String} agreementCode 协议代码
     * @apiParam (请求参数) {String} tokenId 客户端token
     * @apiParam (请求参数) {String} corpId 商户号
     * @apiParam (请求参数) {String} actionId 活动号
     * @apiParam (请求参数) {String} operIp IP
     * @apiParam (请求参数) {String} disCode 分销代码
     * @apiParam (请求参数) {String} outletCode 分销网点号
     * @apiParamExample 请求参数示例
     * fundManCode=7X1e1IYfe&tokenId=qSdLIOD&corpId=1b6OTR&contractNo=9&agreementCode=pLsI1erI&disCode=zK0&appSheetSerialNo=mnfQ7I&number=n1Fhflxuw0&fundCode=hTML&operIp=IPyyrb&actionId=2&fileType=Xxq9J&outletCode=viZJlcGW
     * @apiSuccess (响应结果) {Object} response
     * @apiSuccess {Objectr} stream 下载文件操作，返回的是二进制流
     */
    @RequestMapping("/simu/query/filedownload.htm")
    public void handleRequestInternal(HttpServletRequest request, HttpServletResponse response) throws Exception {
        ContractDownCmd contractDownCmd = getCommand(ContractDownCmd.class);
        try {
            ValidateUtil.assertValid(contractDownCmd);
        } catch (ConstraintsViolatedException e) {
            showDownloadError(e.getMessage() + "，参数错误！", response);
            return;
        } catch (Exception e) {
            showDownloadError("系统错误！", response);
            return;
        }
        String hboneNo = this.getCustSession().getUser().getHboneNo();
        String custNo = this.getCustSession().getUser().getCustNo();
        if (StringUtils.isBlank(hboneNo) && StringUtils.isBlank(custNo)) {
            showDownloadError("交易账号与一账通账号不能同时为空，参数错误！", response);
            return;
        }
        if (StringUtils.isBlank(custNo)) {
            custNo = hboneService.queryCustNoDtoByHbone(hboneNo);
            if (StringUtils.isBlank(custNo)) {
                log.error("handleRequestInternal-根据hboneNo查询客户信息失败, hboneNo={}", hboneNo);
                throw new BizException(BizErrorEnum.SYSTEM_ERROR.getCode(), BizErrorEnum.HBONENO_NOT_EXIST.getDesc());
            }
        }
        FileSdkPathInfo fileSdkPathInfo = getDownFileV2(custNo, contractDownCmd);

        if (fileSdkPathInfo != null && FileSdkUtil.exists(fileSdkPathInfo)) {
            FileDownUtil.doDownloadV3(fileSdkPathInfo, response);
        } else {
            // 返回200时前端会跳转浏览器
            response.setStatus(404);
            showDownloadError("您要下载的文件不存在，请联系您的投顾或客服！", response);
        }
    }


    /**
     * @api {GET} /simu/query/downloadfundfile.htm downloadFundFile
     * @apiVersion 1.0.0
     * @apiGroup FileDownloadController
     * @apiName downloadFundFile
     * @apiDescription 基金合同下载
     * @apiParam (请求参数) {String} fundCode 产品编码
     * @apiParam (请求参数) {String} agreementCode 合同类型code
     * @apiParamExample 请求参数示例
     * fundCode=N&agreementCode=GDky0
     * @apiSuccess (响应结果) {Object} response
     * @apiSuccessExample 响应结果示例
     * null
     */
    @RequestMapping("/simu/query/downloadfundfile.htm")
    public void downloadFundFile(HttpServletRequest request, HttpServletResponse response) throws Exception {
        DownloadFundFileCmd downloadFundFileCmd = getCommand(DownloadFundFileCmd.class);
        try {
            ValidateUtil.assertValid(downloadFundFileCmd);
        } catch (ConstraintsViolatedException e) {
            showDownloadError(e.getMessage() + "，参数错误！", response);
            return;
        } catch (Exception e) {
            showDownloadError("系统错误！", response);
            return;
        }
        FileSdkPathInfo fileSdkPathInfo = getDownloadFundFile(downloadFundFileCmd);
        if (fileSdkPathInfo != null && FileSdkUtil.exists(fileSdkPathInfo)) {
            FileDownUtil.doDownloadV3(fileSdkPathInfo, response);
        } else {
            // 返回200时前端会跳转浏览器
            response.setStatus(404);
            showDownloadError("您要下载的文件不存在，请联系您的投顾或客服！", response);
        }
    }

    /**
     * 获取产品下载文件信息
     */
    private FileSdkPathInfo getDownloadFundFile(DownloadFundFileCmd downloadFundFileCmd) {
        // 1.根据code获取模版
        HighProductAgreementTemplateEnum highProductAgreementTemplateEnum = HighProductAgreementTemplateEnum.getByCode(downloadFundFileCmd.getAgreementCode());
        if (highProductAgreementTemplateEnum == null) {
            log.error("getDownloadFundFile-根绝agreementCode查询不到合同模版,agreementCode={}", downloadFundFileCmd.getAgreementCode());
            return null;
        }
        String templateIdPrefix = null;
        if (HighProductAgreementTemplateEnum.SALE_AGREEMENT.getCode().equals(downloadFundFileCmd.getAgreementCode())) {
            templateIdPrefix = "xsxyfj";
        } else {
            templateIdPrefix = highProductAgreementTemplateEnum.getTemplateIdPrefix();
        }
        // 2.拼接产品合同文件信息
        String fileName = templateIdPrefix + Constant.UNDER_LINE + downloadFundFileCmd.getFundCode() + ".pdf";
        // 3.查询taCode
        HighProductBaseInfoModel highProductBaseInfoModel = fundService.queryFundInfoByFundCode(downloadFundFileCmd.getFundCode());
        if (highProductBaseInfoModel == null) {
            log.error("getDownloadFundFile-根据产品编码查询不到产品基础信息,fundCode={}", downloadFundFileCmd.getFundCode());
            return null;
        }
        // 相对路径
        String relativePath = highProductBaseInfoModel.getTaCode() + File.separator + downloadFundFileCmd.getFundCode();
        // 切webdav路径
        FileSdkPathInfo fileSdkPathInfo = new FileSdkPathInfo();
        fileSdkPathInfo.setFileName(fileName);
        fileSdkPathInfo.setBusinessCode(FilePathStoreBusinessCodeConfig.PUBLIC_FUND_DOC);
        fileSdkPathInfo.setMiddlePath(relativePath);
        return fileSdkPathInfo;
    }

    /**
     * @api {POST} /simu/query/downurl.htm 下载合同
     * @apiVersion 1.0.0
     * @apiGroup SIMU-CGI
     * @apiName /simu/query/downurl.htm
     * @apiDescription 查询合同列表
     * @apiParam (请求参数) {String} fileType
     * @apiParam (请求参数) {String} fundCode
     * @apiParam (请求参数) {String} fundManCode
     * @apiParam (请求参数) {String} contractNo
     * @apiParam (请求参数) {String} appSheetSerialNo
     * @apiParam (请求参数) {String} number
     * @apiParam (请求参数) {String} agreementCode
     * @apiParam (请求参数) {String} tokenId 客户端token
     * @apiParam (请求参数) {String} corpId 商户号
     * @apiParam (请求参数) {String} actionId 活动号
     * @apiParam (请求参数) {String} operIp IP
     * @apiParam (请求参数) {String} disCode 分销代码
     * @apiParam (请求参数) {String} outletCode 分销网点号
     * @apiParamExample 请求参数示例
     * fundManCode=lLKGA3B9LD&tokenId=7JqEg3R1&corpId=GTu4&contractNo=OLGip2wla9&agreementCode=lURKL&disCode=w&appSheetSerialNo=YEqr&number=A6LAaKy3&fundCode=I&operIp=u27t2q&actionId=bMOCA&fileType=jwSSYQvF&outletCode=axy
     * @apiSuccess (响应结果) {String} downUrl 下载地址
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} desc 返回描述
     * @apiSuccessExample 响应结果示例
     * {"code":"w","downUrl":"ZI8","desc":"EtFkc1XDt"}
     */
    @RequestMapping("/simu/query/downurl.htm")
    public void getDownUrl(HttpServletRequest request, HttpServletResponse response) throws Exception {
        ContractDownCmd contractDownCmd = getCommand(ContractDownCmd.class);
        QueryDownUrlDto rst = new QueryDownUrlDto();
        // 参数校验
        ValidateUtil.assertValid(contractDownCmd);
        rst.setCode("0000");
        rst.setDesc("成功");
        String hboneNo = this.getCustSession().getUser().getHboneNo();
        String custNo = this.getCustSession().getUser().getCustNo();
        if (StringUtils.isBlank(hboneNo) && StringUtils.isBlank(custNo)) {
            showDownloadError("交易账号与一账通账号不能同时为空，参数错误！", response);
            return;
        }
        if (StringUtils.isBlank(custNo)) {
            custNo = hboneService.queryCustNoDtoByHbone(hboneNo);
            if (StringUtils.isBlank(custNo)) {
                log.error("getDownUrl-根据hboneNo查询客户信息失败, hboneNo={}", hboneNo);
                throw new BizException(BizErrorEnum.SYSTEM_ERROR.getCode(), BizErrorEnum.HBONENO_NOT_EXIST.getDesc());
            }
        }
        FileSdkPathInfo fileSdkPathInfo = getDownFileV2(custNo, contractDownCmd);
        if (fileSdkPathInfo != null && FileSdkUtil.exists(fileSdkPathInfo)) {
            if (FilePathStoreBusinessCodeConfig.PUBLIC_FUND_DOC.equals(fileSdkPathInfo.getBusinessCode())) {
                String uriPath = simuCcmsServiceRegister.getTradeDocDomain() + File.separator + CGISimuConstants.FUNDDOC + fileSdkPathInfo.getMiddlePath() + fileSdkPathInfo.getFileName();
                rst.setDownUrl(uriPath);
            } else if (FilePathStoreBusinessCodeConfig.PUBLIC_TRADEDOC.equals(fileSdkPathInfo.getBusinessCode())) {
                String uriPath = simuCcmsServiceRegister.getTradeDocDomain() + File.separator + CGISimuConstants.TRADEDOC + fileSdkPathInfo.getMiddlePath() + fileSdkPathInfo.getFileName();
                rst.setDownUrl(uriPath);
            } else {
                rst.setCode("0001");
                rst.setDesc("文件不存在");
            }
        } else {
            rst.setCode("0001");
            rst.setDesc("文件不存在");
        }
        write(rst, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }


    /**
     * @param contractDownCmd
     * @return java.lang.String
     * @Description 获取合同号
     * <AUTHOR>
     * @Date 2018/12/3 14:21
     **/
    private String getControNo(ContractDownCmd contractDownCmd) {
        if (StringUtils.isNotEmpty(contractDownCmd.getContractNo())) {
            return contractDownCmd.getContractNo();
        }
        return contractDownCmd.getAppSheetSerialNo();
    }

    private FileSdkPathInfo getDownFileV2(String txAcctNo, ContractDownCmd contractDownCmd) throws Exception {
        String prefixPath = ControctFileUtil.SIGNED_FILETYPE_WITH_FILEPATH.get(contractDownCmd.getFileType());
        if (StringUtils.isEmpty(prefixPath)) {
            log.info("getDownFile|fileType not exit fileType:{}, fileTypeMap {} ", contractDownCmd.getFileType(),
                    JSON.toJSONString(ControctFileUtil.SIGNED_FILETYPE_WITH_FILEPATH));
            return null;
        }

        FileSdkPathInfo fileSdkPathInfo = new FileSdkPathInfo();
        String fileName;
        if (ControctFileUtil.FileType.VOL_CONFIRM_TYPE.equals(contractDownCmd.getFileType())) {
            // 份额确认书
            fileName = getConfirmFile(contractDownCmd, txAcctNo);
            fileSdkPathInfo.setBusinessCode(FilePathStoreBusinessCodeConfig.VOL_PDF);
            fileSdkPathInfo.setFileName(fileName);
            fileSdkPathInfo.setMiddlePath(contractDownCmd.getFundManCode() + File.separator + contractDownCmd.getFundCode() + File.separator);
        } else if (ControctFileUtil.TEMPLATE_FILETYPES.contains(contractDownCmd.getFileType())) {
            // 无需签名的文件(返回模板)
            fileName = ControctFileUtil.getTemplateFile(contractDownCmd.getFileType(), contractDownCmd.getFundCode(), contractDownCmd.getNumber(), contractDownCmd.getAgreementCode());
            if (fileName != null) {
                fileSdkPathInfo.setBusinessCode(FilePathStoreBusinessCodeConfig.PUBLIC_FUND_DOC);
                fileSdkPathInfo.setFileName(fileName);
                fileSdkPathInfo.setMiddlePath(contractDownCmd.getFundManCode() + File.separator + contractDownCmd.getFundCode() + File.separator);
            }
        } else {
            // 有电子签名的购买/补签协议
            String agreementCode = ControctFileUtil.getAgreementCode(contractDownCmd.getFileType(), contractDownCmd.getNumber(), contractDownCmd.getAgreementCode());
            QueryEsignatureFilePathRequest request = new QueryEsignatureFilePathRequest();
            request.setTxAcctNo(txAcctNo);
            request.setSignatureSid(getControNo(contractDownCmd));
            request.setAgreementCode(agreementCode);
            request.setTaCode(contractDownCmd.getFundManCode());
            QueryEsignatureFilePathResponse response = queryEsignatureFilePathFacade.execute(request);
            fileName = response.getFilePath();
            if (StringUtils.isNotBlank(fileName)) {
                String newFileName = FileSdkUtil.extractFileNameFromUrl(fileName);
                fileSdkPathInfo.setFileName(newFileName);
                fileSdkPathInfo.setMiddlePath(fileName.replace(newFileName, ""));
                if (CGISimuConstants.PROPERTIES_KEY_SIMUFILE_FUNDBASICFILEPATH.equals(prefixPath)) {
                    fileSdkPathInfo.setBusinessCode(FilePathStoreBusinessCodeConfig.PUBLIC_FUND_DOC);
                } else if (CGISimuConstants.PROPERTIES_KEY_SIMUFILE_TRADEFILEPATH.equals(prefixPath)) {
                    fileSdkPathInfo.setBusinessCode(FilePathStoreBusinessCodeConfig.PUBLIC_TRADEDOC);
                }
            }
        }

        if (fileName == null) {
            return null;
        }
        if (!FileSdkUtil.exists(fileSdkPathInfo)) {
            // 如果文件查询不到,可能是走老逻辑,没有合同编号的文件
            if (ControctFileUtil.HISTORY_NOT_CONTANTAINS_CONTROCTNO_FILETYPES.contains(contractDownCmd.getFileType())) {
                fileName = getFileNameWithOutControctNo(contractDownCmd, txAcctNo);
                fileSdkPathInfo.setFileName(fileName);
            }
            if (CGISimuConstants.PROPERTIES_KEY_SIMUFILE_FUNDBASICFILEPATH.equals(prefixPath)) {
                fileSdkPathInfo.setBusinessCode(FilePathStoreBusinessCodeConfig.PUBLIC_FUND_DOC);
            } else if (CGISimuConstants.PROPERTIES_KEY_SIMUFILE_TRADEFILEPATH.equals(prefixPath)) {
                fileSdkPathInfo.setBusinessCode(FilePathStoreBusinessCodeConfig.PUBLIC_TRADEDOC);
            }
            fileSdkPathInfo.setMiddlePath(contractDownCmd.getFundManCode() + File.separator + contractDownCmd.getFundCode() + File.separator);
            if (!FileSdkUtil.exists(fileSdkPathInfo)) {
                log.error("获取不到对应的文件,contractDownCmd={},fileSdkPathInfo={}", JSON.toJSONString(contractDownCmd), JSON.toJSONString(fileSdkPathInfo));
                return null;
            }
        }
        return fileSdkPathInfo;
    }


    /**
     * @param contractDownCmd
     * @param custNo
     * @return java.lang.String
     * @Description 无合同号文件名
     * <AUTHOR>
     * @Date 2018/12/4 14:21
     **/
    private String getFileNameWithOutControctNo(ContractDownCmd contractDownCmd, String custNo) {
        String fileSufix = ControctFileUtil.getFileSufix(contractDownCmd.getFileType(), contractDownCmd.getNumber());
        return fileSufix + "_" + contractDownCmd.getFundCode() + "_" + custNo + ".pdf";
    }

    /**
     * @param contractDownCmd
     * @param custNo
     * @return java.lang.String
     * @Description 确认书文件名
     * <AUTHOR>
     * @Date 2018/12/4 14:22
     **/
    private String getConfirmFile(ContractDownCmd contractDownCmd, String custNo) {
        String controctNo = getControNo(contractDownCmd);
        String fileSufix = ControctFileUtil.getFileSufix(contractDownCmd.getFileType(), contractDownCmd.getNumber());
        ;
        return controctNo + "_" + custNo + "_" + contractDownCmd.getFundCode() + "_" + fileSufix + ".pdf";
    }

    /**
     * 显示下载错误信息
     */
    private void showDownloadError(String errorMsg, HttpServletResponse response) throws Exception {
        log.error("showDownloadError message:{}", errorMsg);
        response.setContentType("text/html;charset=UTF-8");
        response.getOutputStream().write(errorMsg.getBytes());
        response.getOutputStream().flush();
        response.getOutputStream().close();
    }


}
