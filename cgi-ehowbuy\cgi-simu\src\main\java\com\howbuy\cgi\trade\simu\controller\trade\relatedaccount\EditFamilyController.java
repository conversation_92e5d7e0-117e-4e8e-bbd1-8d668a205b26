/**
 * Copyright (c) 2021, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cgi.trade.simu.controller.trade.relatedaccount;

import com.howbuy.acccenter.common.enums.RelatedAccountTypeEnum;
import com.howbuy.acccenter.facade.query.queryrelatedaccount.bean.RelatedAccountBean;
import com.howbuy.acccenter.facade.query.queryrelatedaccount.bean.SubRelatedAccountBean;
import com.howbuy.cgi.common.CGIConstants;
import com.howbuy.cgi.common.enums.BizErrorEnum;
import com.howbuy.cgi.common.exception.BizException;
import com.howbuy.cgi.trade.simu.controller.AbstractSimuCGIController;
import com.howbuy.cgi.trade.simu.service.relatedaccount.MessageService;
import com.howbuy.cgi.trade.simu.service.relatedaccount.RelatedAccountService;
import com.howbuy.trade.account.model.hbone.MemberCustInfoModel;
import com.howbuy.trade.account.service.hbone.HboneService;
import com.howbuy.trade.common.session.model.TradeSession;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 请在此添加描述
 * <AUTHOR>
 * @date 2021/7/9 16:19
 * @since JDK 1.8
 */
@Controller
public class EditFamilyController extends AbstractSimuCGIController {

    private static final Logger LOG = LogManager.getLogger(EditFamilyController.class);

    @Autowired
    private RelatedAccountService relatedAccountService;
    @Autowired
    private HboneService hboneService;
    @Autowired
    private MessageService messageService;

    /**
     * @api {POST} /simu/relatedaccount/cancelfamily.htm 注销家庭
     * @apiVersion 1.0.0
     * @apiGroup RELATED-ACCOUNT
     * @apiName cancelFamily
     * @apiDescription 注销家庭
     * @apiSuccess (响应结果) {Object} response
     * @apiSuccessExample 响应结果示例
     * null
     */
    @RequestMapping("/simu/relatedaccount/cancelfamily.htm")
    public void cancelFamily(HttpServletRequest request, HttpServletResponse response) throws Exception {
        TradeSession loginInfo = this.getCustSession();
        String hboneNo = loginInfo.getUser().getHboneNo();
        // 查询家庭账户id
        RelatedAccountBean account = relatedAccountService.queryFamilyRelatedAccount(hboneNo, null);
        if (account == null) {
            throw new BizException(BizErrorEnum.SYSTEM_ERROR.getCode(), "未找到家庭账户");
        }
        // 注销家庭
        List<SubRelatedAccountBean> subAccountList = relatedAccountService.closeRelatedAccount(hboneNo, account.getRelatedAccountId());
        // 发送消息
        sendCancelFamilyMsg(hboneNo, loginInfo.getUser().getCustName(), subAccountList);

        write("成功", CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }

    /**
     * 发送注销家庭消息
     * @param hboneNo
     * @param custName
     * @param subAccountList
     * @return void
     * @author: huaqiang.liu
     * @date: 2021/7/21 10:43
     * @since JDK 1.8
     */
    private void sendCancelFamilyMsg(String hboneNo, String custName, List<SubRelatedAccountBean> subAccountList) {
        try {
            // 发给家庭主投顾
            Map<String, Object> consParams = new HashMap<>(1);
            consParams.put("custName2", custName);
            messageService.sendMsgToConsultant("200244", hboneNo, consParams);

            // 无副账户的家庭账户注销不发个人短信
            if (CollectionUtils.isEmpty(subAccountList)) {
                return;
            }
            // 查询所有家庭成员的信息
            StringBuilder memberNames = new StringBuilder();
            for (SubRelatedAccountBean bean : subAccountList) {
                MemberCustInfoModel custInfo = hboneService.queryCustInfoByHboneNo(bean.getSubHboneNo());
                if (memberNames.length()>0) {
                    memberNames.append("、").append(custInfo.getCustName());
                } else {
                    memberNames.append(custInfo.getCustName());
                }
            }
            // 发给家庭主
            Map<String, Object> params = new HashMap<>(1);
            params.put("custName", memberNames.toString());
            messageService.sendByHboneNo("60395", hboneNo, params);
            // 发送给家庭成员
            Map<String, Object> memberParams = new HashMap<>(1);
            memberParams.put("custName", custName);
            for (SubRelatedAccountBean bean : subAccountList) {
                messageService.sendByHboneNo("60394", bean.getSubHboneNo(), memberParams);
            }
        } catch (Throwable e) {
            log.error("sendCancelFamilyMsg fail. error:{}", e.getMessage(), e);
        }
    }

    /**
     * @api {post}  /simu/relatedaccount/removefamilymember.htm 移除家庭成员
     * @apiGroup RELATED-ACCOUNT
     * @APIName /simu/relatedaccount/removefamilymember.htm
     * @apiDescription 移除家庭成员
     *
     * @apiParam {String} subAccountId 子关联账户id
     *
     * @apiSuccess {String} desc 返回描述
     * @apiSuccess {String} code 返回码
     * @apiSuccess {Object} body 数据
     *
     */
    @RequestMapping("/simu/relatedaccount/removefamilymember.htm")
    public void removeFamilyMember(HttpServletRequest request, HttpServletResponse response) throws Exception {
        TradeSession loginInfo = this.getCustSession();
        // 家庭成员 子关联账户id
        String subAccountId = getString("subAccountId");
        if (StringUtils.isBlank(subAccountId)) {
            throw new BizException(BizErrorEnum.REQUEST_PARAMS_ERROR.getCode(), BizErrorEnum.REQUEST_PARAMS_ERROR.getDesc());
        }

        // 查询家庭账户id
        String hboneNo = loginInfo.getUser().getHboneNo();
        RelatedAccountBean account = relatedAccountService.queryFamilyRelatedAccount(hboneNo, null);
        if (account == null) {
            throw new BizException(BizErrorEnum.SYSTEM_ERROR.getCode(), "家庭账户不存在");
        }
        // 移除家庭成员
        relatedAccountService.closeSubRelatedAccount(account.getRelatedAccountId(), subAccountId);
        // 发送消息
        SubRelatedAccountBean subAccount = null;
        for (SubRelatedAccountBean bean : account.getSubRelatedAccountList()) {
            if (subAccountId.equals(bean.getSubRelatedAccountId())) {
                subAccount = bean;
                break;
            }
        }
        if (subAccount == null) {
            LOG.error("removeFamilyMember|can not send msg, not find subAccount.");
        } else {
            sendRemoveFamilyMemberMsg(hboneNo, loginInfo.getUser().getCustName(), subAccount);
        }

        write("成功", CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }

    /**
     * 发送移除家庭成员消息
     * @param hboneNo
     * @param custName
     * @param subAccount
     * @return void
     * @author: huaqiang.liu
     * @date: 2021/7/21 11:20
     * @since JDK 1.8
     */
    private void sendRemoveFamilyMemberMsg(String hboneNo, String custName, SubRelatedAccountBean subAccount) {
        try {
            // 发送给家庭成员
            Map<String, Object> memberParams = new HashMap<>(1);
            memberParams.put("custName", custName);
            messageService.sendByHboneNo("60394", subAccount.getSubHboneNo(), memberParams);

            // 查询家庭成员的信息
            MemberCustInfoModel custInfo = hboneService.queryCustInfoByHboneNo(subAccount.getSubHboneNo());
            // 发给家庭主
            Map<String, Object> params = new HashMap<>(1);
            params.put("custName", custInfo.getCustName());
            messageService.sendByHboneNo("60395", hboneNo, params);
            // 发给家庭主投顾
            Map<String, Object> consParams = new HashMap<>(2);
            consParams.put("custName2", custName);
            messageService.sendMsgToConsultant("200222", hboneNo, consParams);

        } catch (Throwable e) {
            log.error("sendRemoveFamilyMemberMsg fail. error:{}", e.getMessage(), e);
        }
    }

    /**
     * @api {post}  /simu/relatedaccount/quitfamily.htm 退出家庭
     * @apiGroup RELATED-ACCOUNT
     * @APIName /simu/relatedaccount/quitfamily.htm
     * @apiDescription 退出家庭
     *
     * @apiParam {String} accountId 家庭关联账户id
     *
     * @apiSuccess {String} desc 返回描述
     * @apiSuccess {String} code 返回码
     * @apiSuccess {Object} body 数据
     *
     */
    @RequestMapping("/simu/relatedaccount/quitfamily.htm")
    public void quitFamily(HttpServletRequest request, HttpServletResponse response) throws Exception {
        TradeSession loginInfo = this.getCustSession();

        // 家庭关联账户id
        String accountId = getString("accountId");
        if (StringUtils.isBlank(accountId)) {
            throw new BizException(BizErrorEnum.REQUEST_PARAMS_ERROR.getCode(), BizErrorEnum.REQUEST_PARAMS_ERROR.getDesc());
        }

        // 查询家庭账户id
        String hboneNo = loginInfo.getUser().getHboneNo();
        SubRelatedAccountBean account = relatedAccountService.querySubRelatedAccount(hboneNo, RelatedAccountTypeEnum.FAMILY_0);
        if (account == null) {
            throw new BizException(BizErrorEnum.SYSTEM_ERROR.getCode(), "家庭账户不存在");
        }
        // 退出家庭
        relatedAccountService.closeSubRelatedAccount(accountId, account.getSubRelatedAccountId());
        // 发送消息
        sendQuitFamilyMsg(hboneNo, loginInfo.getUser().getCustName(), accountId);

        write("成功", CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }

    /**
     * 发送退出家庭消息
     * @param hboneNo
     * @param memberCustName
     * @param accountId
     * @return void
     * @author: huaqiang.liu
     * @date: 2021/7/21 11:32
     * @since JDK 1.8
     */
    private void sendQuitFamilyMsg(String hboneNo, String memberCustName, String accountId) {
        try {
            // 查询家庭主一账通
            RelatedAccountBean account = relatedAccountService.queryFamilyRelatedAccount(null, accountId);
            // 查询家庭主信息
            MemberCustInfoModel masterCustInfo = hboneService.queryCustInfoByHboneNo(account.getHboneNo());

            // 发给家庭主
            Map<String, Object> params = new HashMap<>(1);
            params.put("custName", memberCustName);
            messageService.sendByHboneNo("60376", account.getHboneNo(), params);

            // 发送给家庭成员
            Map<String, Object> memberParams = new HashMap<>(1);
            memberParams.put("custName", masterCustInfo.getCustName());
            messageService.sendByHboneNo("60377", hboneNo, memberParams);
            // 发给家庭成员投顾
            Map<String, Object> consParams = new HashMap<>(2);
            consParams.put("custName1", memberCustName);
            messageService.sendMsgToConsultant("200242", hboneNo, consParams);

        } catch (Throwable e) {
            log.error("sendRemoveFamilyMemberMsg fail. error:{}", e.getMessage(), e);
        }
    }
}