package com.howbuy.cgi.trade.simu.interceptor;

import com.howbuy.cgi.common.CGIConstants;
import com.howbuy.cgi.common.enums.BizErrorEnum;
import com.howbuy.cgi.common.exception.BizException;
import com.howbuy.cgi.common.remote.param.RemoteParametersProvider;
import com.howbuy.cgi.common.session.SessionContextHolderFactory;
import com.howbuy.cgi.common.session.model.CustomerSession;
import com.howbuy.cgi.common.util.CgiParamUtil;
import com.howbuy.cgi.common.util.RequestUtil;
import com.howbuy.cgi.trade.ccms.simu.SimuCcmsServiceRegister;
import com.howbuy.cgi.trade.simu.model.dto.QueryComplianceStateDto;
import com.howbuy.cgi.trade.simu.model.vo.CustomerComplianceStateVo;
import com.howbuy.cgi.trade.simu.service.relatedaccount.AccCenterService;
import com.howbuy.cgi.trade.simu.util.BusiUtil;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.utils.StringUtils;
import com.howbuy.trade.common.session.model.TradeSession;
import com.howbuy.trade.common.session.model.UserInfo;
import com.howbuy.trade.common.session.wap.WapTradeSession;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

public abstract class AbstractStatusInterceptor extends HandlerInterceptorAdapter {
    protected final Logger LOG = LoggerFactory.getLogger(getClass());
    @Autowired
    private SimuCcmsServiceRegister simuCcmsServiceRegister;
    protected List<String> excludes = null;
    //私募用户的kyc信息;
    private static final String SIMU_KYC_INFO = "SIMU_KYC_INFO_NEW";


    @Override
    public abstract boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception;

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        super.afterCompletion(request, response, handler, ex);
    }

    protected TradeSession getCustSession() {
        String corpId = CgiParamUtil.getCorpId();
        if ("000003".equals(corpId) || "000005".equals(corpId)) {
            // 微信登录或网站登录
            WapTradeSession pigsession = (WapTradeSession) RequestUtil.getHttpRequest().getSession()
                    .getAttribute(CGIConstants.LOGIN_SESSION_NAME);
            if (pigsession != null) {
                return pigsession;
            }
        } else {
            // app登录
            CustomerSession customerSession = SessionContextHolderFactory.getSessionContextHolder().getCustSession(
                    CgiParamUtil.getCorpId(), RequestUtil.getParameter("custNo"), CgiParamUtil.getHboneNo(),
                    CgiParamUtil.getTokenId());
            if (customerSession != null) {
                if (customerSession.getUser() != null) {
                    customerSession.getUser().setHboneNo(CgiParamUtil.getHboneNo());
                } else {
                    UserInfo user = new UserInfo();
                    user.setHboneNo(CgiParamUtil.getHboneNo());
                    customerSession.setUser(user);

                }

                return customerSession;
            }
        }

        return null;
    }

    protected void isLogin(TradeSession loginInfo) {
        if (null == loginInfo || null == loginInfo.getUser() || null == loginInfo.getUser().getHboneNo()) {
            if (null != loginInfo) {
                LOG.info("null == loginInfo.getUser() ?? {}", null == loginInfo.getUser());
            }
            if (null != loginInfo && null != loginInfo.getUser()) {
                LOG.info("null == loginInfo.getUser().getHboneNo() ?? {}", null == loginInfo.getUser().getHboneNo());
            }
            throw new BizException(BizErrorEnum.HIGH_USER_NOT_LOGIN.getCode(), "用户尚未登录");
        }
    }

    protected boolean isExcludes(String url) {
        if (StringUtils.isNotBlank(simuCcmsServiceRegister.getExcludePatch())) {
            String[] excludes = simuCcmsServiceRegister.getExcludePatch().split(";");
            for (String e : excludes) {
                if (url.endsWith(e)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 校验kyc信息
     *
     * @param request
     */
    public boolean checkKyc(HttpServletRequest request, AccCenterService accCenterService) {
        TradeSession loginInfo = getCustSession();
        isLogin(loginInfo);

        // 未开户的用户访问持仓首页无需校验kyc（仅有持仓用户才能访问）
        if (notNeedKycValidate(request.getRequestURI())) {
            return true;
        }
        // 获取分销渠道
        String disCode = getDisCode(request);

        CustomerComplianceStateVo customerComplianceStateVo = accCenterService.queryComplianceState(loginInfo.getUser().getHboneNo(), disCode);
        if (CollectionUtils.isEmpty(customerComplianceStateVo.getComplianceStateInfoList())) {
            throw new BizException(BizErrorEnum.ACCOUNT_NO_FUND.getCode(), "没有检查到已激活开户信息");
        }
        for (CustomerComplianceStateVo.ComplianceStateInfo complianceStateInfo : customerComplianceStateVo.getComplianceStateInfoList()) {
            QueryComplianceStateDto queryComplianceStateDto = complianceStateInfo.getQueryComplianceStateDto();
            if (YesOrNoEnum.NO.getCode().equals(queryComplianceStateDto.getElecSignFlag())) {
                throw new BizException(BizErrorEnum.HIGH_SIGN_APPOINTMENT_LETTER_PRO.getCode(), "请签署电子约定书");
            }
            if (!YesOrNoEnum.YES.getCode().equals(queryComplianceStateDto.getRiskFlag())) {
                throw new BizException(BizErrorEnum.PROINVESTO_KYC_INVALID.getCode(), BizErrorEnum.PROINVESTO_KYC_INVALID.getDesc());
            }
            if (!BusiUtil.isProInvestor(queryComplianceStateDto.getInvestorType())) {
                if (!"1".equals(queryComplianceStateDto.getSignFlag())) {
                    throw new BizException(BizErrorEnum.HIGH_INVESTOR_CONFIRME_LETTER.getCode(), "非私募投资者,需要签署协议");
                }
            }
        }
        return true;
    }

    private String getDisCode(HttpServletRequest request) {
        String disCode = RemoteParametersProvider.getDistributionCode();
        // 非交易相关的页面,开什么户就校验什么kyc,跟交易相关的,什么分销就校验对应的kyc
        if (request.getRequestURI().endsWith("/simu/user/balance.htm") || request.getRequestURI().endsWith("/simu/user/balanceV2.htm")) {
            return null;
        }
        //如果获取不到分销渠道,就认为是好买的
        if (StringUtils.isBlank(disCode)) {
            return DisCodeEnum.HM.getCode();
        }
        // 非好臻就认为是好买
        if (!DisCodeEnum.HZ.getCode().equals(disCode)) {
            return DisCodeEnum.HM.getCode();
        }
        return disCode;
    }


    /**
     * 需要校验kyc信息的页面
     */
    protected boolean notNeedKycValidate(String uri) {
        // 下单页需要校验kyc,其他不需要
        if (uri.endsWith("/simu/trade/buy.htm")) {
            return false;
        }
        // 下单页
        return !uri.endsWith("/simu/trade/buySubmit.htm");
    }
}
