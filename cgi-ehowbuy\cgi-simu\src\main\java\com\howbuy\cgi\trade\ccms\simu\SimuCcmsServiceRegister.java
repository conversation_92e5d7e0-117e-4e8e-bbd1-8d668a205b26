package com.howbuy.cgi.trade.ccms.simu;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

@Configuration
@RefreshScope
@Component
public class SimuCcmsServiceRegister {

    /**
     * 随机数范围
     */
    @Value("${cgi-simu.DIGITS_RANK}")
    private String digitsRank;

    /**
     * 最大校验次数
     */
    @Value("${cgi-simu.VERIFY_MAX_COUNT}")
    private int verifyMaxCount;

    /**
     * 最大校验次数
     */
    @Value("${cgi-simu.needShowHz}")
    private String needShowHz;

    /**
     * 预约缓存过期时间
     */
    @Value("${cgi-simu.PREINFO_CACHE_EXPIRE_TIMES}")
    private Integer preinfoCachExpireTimes;

    /**
     * 交易文档域名
     **/
    @Value("${cgi-simu.TRADE_DOC_DOMAIN}")
    private String tradeDocDomain;

    /**
     * 交易文档域名
     **/
    @Value("${cgi-simu.TRADE_DOC_PATH}")
    private String tradeDocPath;

    /**
     * 好买鉴权短信银行
     **/
    @Value("${tms-high-order-center.bankCodeFilter}")
    private String bankCodeFilter;

    /**
     * 打款银行切换日yyyyMMdd
     **/
    @Value("${cgi-simu.PAYMENT_BANK_CHANGE_DT}")
    private String paymentBankChangeDt;

    /**
     * 打款银行切换提醒结束日yyyyMMdd
     **/
    @Value("${cgi-simu.PAYMENT_BANK_CHANGE_END_DT}")
    private String paymentBankChangeEndDt;

    /**
     * 原打款银行和新打款银行对应[{"oldAccountId":"12","oldBankCode":"102", "oldBankName":"民生银行"}, {"newAccountId":"34","newBankCode":"104", "newBankName":"浦发银行"}]
     **/
    @Value("${cgi-simu.oldAndNewPaymentBank}")
    private String oldAndNewPaymentBank;

    /**
     * 鉴权通道
     **/
    @Value("${tms-high-order-center.authChannel}")
    private String authChannel;

    /**
     * 校验邮箱是否未空
     **/
    @Value("${cgi-simu.valiateEmail}")
    private String valiateEmail;

    /**
     * 臻财VIP绑定白名单（这批用户不支持自动登录，每次都要输入密码）
     **/
    @Value("${cgi-simu.zcvipBindWhite}")
    private String zcvipBindWhite;

    /**
     * 邀请码查询地址
     */
    @Value("${cgi-simu.RELATED_ACCOUNT_INVITE_CODE_URL}")
    private String inviteCodeUrl;

    /**
     * 私募首页_收益按钮_需要隐藏的产品
     */
    @Value("${cgi-simu.shou_yi_hide_fund}")
    private String shouYiHideFund;

    /**
     * 私募首页_收益按钮_需要隐藏的一账通
     */
    @Value("${cgi-simu.shou_yi_hide_hbOneNo}")
    private String shouYiHideHbOneNo;

    /**
     * 私募首页_理财分析按钮_需要隐藏的产品
     */
    @Value("${cgi-simu.li_cai_hide_fund}")
    private String liCaiHideFund;

    /**
     * 私募首页_理财分析按钮_需要隐藏的一账通
     */
    @Value("${cgi-simu.li_cai_hide_hbOneNo}")
    private String liCaiHideHbOneNo;

    /**
     * 资产详情_收益tab_需要隐藏的产品
     */
    @Value("${cgi-simu.shou_yi_tab_hide_fund}")
    private String shouYiTabHideFund;

    /**
     * 手机验证是否走固定配置值
     */
    @Value("${cgi-simu.mobile_auth_msg_use_cfg}")
    private String mobileAuthMsgUseCfg;

    /**
     * 资产详情_收益tab_需要隐藏的一账通
     */
    @Value("${cgi-simu.shou_yi_tab_hide_hbOneNo}")
    private String shouYiTabHideHbOneNo;


    /**
     * 高风险确认书默认内容
     */
    @Value("${cgi-simu.high_risk_confirm_default_content}")
    private String highRiskConfirmDefaultContent;

    /**
     * 在配置中的,不校验用户登录信息
     */
    @Value("${cgi-simu.excludePatch}")
    private String excludePatch;


    public String getExcludePatch() {
        return excludePatch;
    }

    public void setExcludePatch(String excludePatch) {
        this.excludePatch = excludePatch;
    }

    /**
     * 风测提醒确认时间校验开关
     */
    @Value("${cgi-simu.risk_hint_confirm_dtm_verify}")
    private String riskHintConfirmDtmVerify;

    public String getMobileAuthMsgUseCfg() {
        return mobileAuthMsgUseCfg;
    }

    public String getHighRiskConfirmDefaultContent() {
        return highRiskConfirmDefaultContent;
    }

    public void setHighRiskConfirmDefaultContent(String highRiskConfirmDefaultContent) {
        this.highRiskConfirmDefaultContent = highRiskConfirmDefaultContent;
    }

    public void setMobileAuthMsgUseCfg(String mobileAuthMsgUseCfg) {
        this.mobileAuthMsgUseCfg = mobileAuthMsgUseCfg;
    }

    public String getShouYiHideFund() {
        return shouYiHideFund;
    }

    public void setShouYiHideFund(String shouYiHideFund) {
        this.shouYiHideFund = shouYiHideFund;
    }

    public String getShouYiHideHbOneNo() {
        return shouYiHideHbOneNo;
    }

    public String getNeedShowHz() {
        return needShowHz;
    }

    public void setNeedShowHz(String needShowHz) {
        this.needShowHz = needShowHz;
    }

    public void setShouYiHideHbOneNo(String shouYiHideHbOneNo) {
        this.shouYiHideHbOneNo = shouYiHideHbOneNo;
    }

    public String getLiCaiHideFund() {
        return liCaiHideFund;
    }

    public void setLiCaiHideFund(String liCaiHideFund) {
        this.liCaiHideFund = liCaiHideFund;
    }

    public String getLiCaiHideHbOneNo() {
        return liCaiHideHbOneNo;
    }

    public void setLiCaiHideHbOneNo(String liCaiHideHbOneNo) {
        this.liCaiHideHbOneNo = liCaiHideHbOneNo;
    }

    public String getShouYiTabHideFund() {
        return shouYiTabHideFund;
    }

    public void setShouYiTabHideFund(String shouYiTabHideFund) {
        this.shouYiTabHideFund = shouYiTabHideFund;
    }

    public String getShouYiTabHideHbOneNo() {
        return shouYiTabHideHbOneNo;
    }

    public void setShouYiTabHideHbOneNo(String shouYiTabHideHbOneNo) {
        this.shouYiTabHideHbOneNo = shouYiTabHideHbOneNo;
    }

    public String getDigitsRank() {
        return digitsRank;
    }

    public void setDigitsRank(String digitsRank) {
        this.digitsRank = digitsRank;
    }

    public int getVerifyMaxCount() {
        return verifyMaxCount;
    }

    public void setVerifyMaxCount(int verifyMaxCount) {
        this.verifyMaxCount = verifyMaxCount;
    }

    public Integer getPreinfoCachExpireTimes() {
        return preinfoCachExpireTimes;
    }

    public void setPreinfoCachExpireTimes(Integer preinfoCachExpireTimes) {
        this.preinfoCachExpireTimes = preinfoCachExpireTimes;
    }

    public String getTradeDocDomain() {
        return tradeDocDomain;
    }

    public void setTradeDocDomain(String tradeDocDomain) {
        this.tradeDocDomain = tradeDocDomain;
    }

    public String getTradeDocPath() {
        return tradeDocPath;
    }

    public void setTradeDocPath(String tradeDocPath) {
        this.tradeDocPath = tradeDocPath;
    }

    public String getBankCodeFilter() {
        return bankCodeFilter;
    }

    public void setBankCodeFilter(String bankCodeFilter) {
        this.bankCodeFilter = bankCodeFilter;
    }

    public String getPaymentBankChangeDt() {
        return paymentBankChangeDt;
    }

    public void setPaymentBankChangeDt(String paymentBankChangeDt) {
        this.paymentBankChangeDt = paymentBankChangeDt;
    }

    public String getPaymentBankChangeEndDt() {
        return paymentBankChangeEndDt;
    }

    public void setPaymentBankChangeEndDt(String paymentBankChangeEndDt) {
        this.paymentBankChangeEndDt = paymentBankChangeEndDt;
    }

    public String getOldAndNewPaymentBank() {
        return oldAndNewPaymentBank;
    }

    public void setOldAndNewPaymentBank(String oldAndNewPaymentBank) {
        this.oldAndNewPaymentBank = oldAndNewPaymentBank;
    }

    public String getAuthChannel() {
        return authChannel;
    }

    public void setAuthChannel(String authChannel) {
        this.authChannel = authChannel;
    }

    public String getValiateEmail() {
        return valiateEmail;
    }

    public void setValiateEmail(String valiateEmail) {
        this.valiateEmail = valiateEmail;
    }

    public String getZcvipBindWhite() {
        return zcvipBindWhite;
    }

    public void setZcvipBindWhite(String zcvipBindWhite) {
        this.zcvipBindWhite = zcvipBindWhite;
    }

    public String getInviteCodeUrl() {
        return inviteCodeUrl;
    }

    public void setInviteCodeUrl(String inviteCodeUrl) {
        this.inviteCodeUrl = inviteCodeUrl;
    }

    public String getRiskHintConfirmDtmVerify() {
        return riskHintConfirmDtmVerify;
    }

    public void setRiskHintConfirmDtmVerify(String riskHintConfirmDtmVerify) {
        this.riskHintConfirmDtmVerify = riskHintConfirmDtmVerify;
    }

    public boolean isVerifyRiskHintConfirmDtm() {
        return "ON".equals(this.riskHintConfirmDtmVerify);
    }
}
