<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.12.RELEASE</version>
        <relativePath /> 
    </parent>

    <groupId>com.howbuy.tms</groupId>
    <artifactId>order-center-new</artifactId>
    <version>4.8.82-RELEASE</version>
    <packaging>pom</packaging>
    <name>order-center-new</name>
    <description>order-center-new</description>
    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <spring-boot.version>2.2.2.RELEASE</spring-boot.version>
        <spring-cloud.version>Hoxton.SR12</spring-cloud.version>
        <mybatis.version>2.2.0</mybatis.version>
        <cloud-common.version>0.1.5-RELEASE</cloud-common.version>
        <druid.version>1.2.8</druid.version>
        <pagehelper.version>4.1.4</pagehelper.version>
        <fastjson.version>1.2.83</fastjson.version>
        <dubbo.version>2.7.15</dubbo.version>
        <curator.version>4.2.0</curator.version>
        <zookeeper.version>3.4.13</zookeeper.version>
        <zkclient.version>0.4</zkclient.version>
        <lombok.version>1.18.16</lombok.version>
        <log4j.version>2.15.0</log4j.version>
        <org.mapstruct.version>1.5.5.Final</org.mapstruct.version>
        <rocketmq.client.version>4.9.0</rocketmq.client.version>

        <com.howbuy.spring-context-support.version>1.0.11</com.howbuy.spring-context-support.version>
        <com.howbuy.howbuy-sharding-id.version>2.0.4-RELEASE</com.howbuy.howbuy-sharding-id.version>

        <com.howbuy.howbuy-boot-actuator.version>2.3.0-RELEASE</com.howbuy.howbuy-boot-actuator.version>
        <com.howbuy.ccms-watcher-plugin-nacosImpl.version>1.0.0-RELEASE</com.howbuy.ccms-watcher-plugin-nacosImpl.version>
        <com.howbuy.howbuy-ccms-watcher.version>6.0.1-RELEASE</com.howbuy.howbuy-ccms-watcher.version>
        <com.howbuy.howbuy-cachemanagement.version>3.8.0-RELEASE</com.howbuy.howbuy-cachemanagement.version>
        <com.howbuy.howbuy-message-service.version>2.3.0-RELEASE</com.howbuy.howbuy-message-service.version>
        <com.howbuy.howbuy-message-amq.version>2.3.0-RELEASE</com.howbuy.howbuy-message-amq.version>
        <com.howbuy.howbuy-message-rocket.version>2.3.0-RELEASE</com.howbuy.howbuy-message-rocket.version>
        <com.howbuy.howbuy-auth-facade.version>2.2.0-RELEASE</com.howbuy.howbuy-auth-facade.version>
        <com.howbuy.howbuy-cms-client.version>release-20250526-cms-submit-RELEASE</com.howbuy.howbuy-cms-client.version>
        <com.alibaba.csp.sentinel-core.version>1.8.6</com.alibaba.csp.sentinel-core.version>
        <com.alibaba.csp.sentinel-transport-simple-http.version>1.8.6</com.alibaba.csp.sentinel-transport-simple-http.version>
        <com.howbuy.howbuy-fund-client.version>release-20250521-4018-gyxj-RELEASE</com.howbuy.howbuy-fund-client.version>
        <com.howbuy.howbuy-cms-client.version>release-20250526-cms-submit-RELEASE</com.howbuy.howbuy-cms-client.version>

        <com.howbuy.tms-common-aop.version>4.7.53-RELEASE</com.howbuy.tms-common-aop.version>
        <com.howbuy.tms-common-log-pattern.version>1.0.0-RELEASE</com.howbuy.tms-common-log-pattern.version>

        <com.howbuy.tms-common-enums.version>4.8.37-RELEASE</com.howbuy.tms-common-enums.version>
        <com.howbuy.tms-common-service.version>4.8.37-RELEASE</com.howbuy.tms-common-service.version>
        <com.howbuy.tms-common-client.version>4.8.37-RELEASE</com.howbuy.tms-common-client.version>
        <com.howbuy.tms-common-cache.version>4.8.37-RELEASE</com.howbuy.tms-common-cache.version>
        <com.howbuy.tms-common-message-service.version>4.8.37-RELEASE</com.howbuy.tms-common-message-service.version>

        <com.howbuy.center-client.version>6.4.9-RELEASE</com.howbuy.center-client.version>
        <com.howbuy.acc-center-facade.version>20250702-RELEASE</com.howbuy.acc-center-facade.version>
        <com.howbuy.fin-online-facade.version>3.3.5-RELEASE</com.howbuy.fin-online-facade.version>
        <com.howbuy.fbs-online-search-facade.version>3.40.1-RELEASE</com.howbuy.fbs-online-search-facade.version>
        <com.howbuy.fbs-online-facade.version>3.40.0-RELEASE</com.howbuy.fbs-online-facade.version>

        <com.howbuy.ftx-online-search-facade.version>1.1.32-RELEASE</com.howbuy.ftx-online-search-facade.version>
        <com.howbuy.high-order-center-client.version>4.8.70-RELEASE</com.howbuy.high-order-center-client.version>
        <com.howbuy.param-server-facade.version>20250616-RELEASE</com.howbuy.param-server-facade.version>
        <com.howbuy.high-order-center-client.version>4.8.70-RELEASE</com.howbuy.high-order-center-client.version>
        <com.howbuy.message-center-client.version>4.7.47-RELEASE</com.howbuy.message-center-client.version>
        <com.howbuy.elasticsearch-center-client.version>4.8.25-RELEASE</com.howbuy.elasticsearch-center-client.version>

        <com.howbuy.product-center-model.version>4.8.50-RELEASE</com.howbuy.product-center-model.version>
        <com.howbuy.product-center-client.version>4.8.50-RELEASE</com.howbuy.product-center-client.version>

        <com.howbuy.order-center-new.version>4.8.82-RELEASE</com.howbuy.order-center-new.version>
        <com.howbuy.order-center-interaction.version>4.8.82-RELEASE</com.howbuy.order-center-interaction.version>
        <com.howbuy.order-center.version>4.8.80-RELEASE</com.howbuy.order-center.version>

        <com.howbuy.order-plan-center.version>4.8.57-RELEASE</com.howbuy.order-plan-center.version>
        <com.howbuy.order-plan-center-model.version>20250711-001-RELEASE</com.howbuy.order-plan-center-model.version>
        <com.howbuy.order-plan-center-client.version>20250711-001-RELEASE</com.howbuy.order-plan-center-client.version>

        <com.howbuy.robot-order-center-client.version>4.8.80-RELEASE</com.howbuy.robot-order-center-client.version>
        <com.howbuy.robot-order-center.version>4.8.80-RELEASE</com.howbuy.robot-order-center.version>
        <com.howbuy.batch-center-client.version>4.8.57-RELEASE</com.howbuy.batch-center-client.version>
        <com.howbuy.batch-center.version>4.8.82-RELEASE</com.howbuy.batch-center.version>
        <com.howbuy.unique-field-processor-tools.version>1.0.4-RELEASE</com.howbuy.unique-field-processor-tools.version>


    </properties>
    <dependencyManagement>
        <dependencies>
            
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo</artifactId>
                <version>${dubbo.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-spring-boot-starter</artifactId>
                <version>${dubbo.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.zookeeper</groupId>
                <artifactId>zookeeper</artifactId>
                <version>${zookeeper.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-dependencies-zookeeper</artifactId>
                <version>${dubbo.version}</version>
                <type>pom</type>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-recipes</artifactId>
                <version>${curator.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-client</artifactId>
                <version>${curator.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-framework</artifactId>
                <version>${curator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>${pagehelper.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>2.2.1.RELEASE</version>
                <scope>test</scope>
                <exclusions>
                    <exclusion>
                        <artifactId>spring-boot-starter-logging</artifactId>
                        <groupId>org.springframework.boot</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.howbuy.tms</groupId>
                <artifactId>tms-common-aop</artifactId>
                <version>${com.howbuy.tms-common-aop.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
                <version>2.2.0.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
                <version>2.2.0.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-message-service</artifactId>
                <version>${com.howbuy.howbuy-message-service.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-message-amq</artifactId>
                <version>${com.howbuy.howbuy-message-amq.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>activemq-all</artifactId>
                        <groupId>org.apache.activemq</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-message-rocket</artifactId>
                <version>${com.howbuy.howbuy-message-rocket.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.howbuy.pa</groupId>
                <artifactId>ccms-watcher-plugin-nacosImpl</artifactId>
                <version>${com.howbuy.ccms-watcher-plugin-nacosImpl.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>fastjson</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>nacos-client</artifactId>
                        <groupId>com.alibaba.nacos</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-ccms-watcher</artifactId>
                <version>${com.howbuy.howbuy-ccms-watcher.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>fastjson</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            
            <dependency>
                <artifactId>howbuy-cachemanagement</artifactId>
                <groupId>com.howbuy</groupId>
                <version>${com.howbuy.howbuy-cachemanagement.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>*</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>javassist</artifactId>
                        <groupId>org.javassist</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>zkutils</artifactId>
                        <groupId>com.howbuy</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>howbuy-ccms-independent</artifactId>
                        <groupId>com.howbuy</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>fastjson</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>activemq-all</artifactId>
                        <groupId>org.apache.activemq</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>4.3.2</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>2.11.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-client</artifactId>
                <version>${rocketmq.client.version}</version>
            </dependency>
            
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>zkutils</artifactId>
                <version>1.1</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.spring</groupId>
                <artifactId>spring-context-support</artifactId>
                <version>${com.howbuy.spring-context-support.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.boot</groupId>
                <artifactId>howbuy-boot-actuator</artifactId>
                <version>${com.howbuy.howbuy-boot-actuator.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>*</artifactId>
                        <groupId>ch.qos.logback</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>mockito-all</artifactId>
                        <groupId>org.mockito</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-auth-facade</artifactId>
                <version>${com.howbuy.howbuy-auth-facade.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <artifactId>guava</artifactId>
                <groupId>com.google.guava</groupId>
                <version>20.0</version>
            </dependency>
            
            <dependency>
                <groupId>com.howbuy.acccenter</groupId>
                <artifactId>acc-center-facade</artifactId>
                <version>${com.howbuy.acc-center-facade.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>utils</artifactId>
                <version>1.0.0-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <artifactId>javassist</artifactId>
                        <groupId>org.javassist</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-fund-client</artifactId>
				<version>${com.howbuy.howbuy-fund-client.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
			</dependency>

            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-cms-client</artifactId>
                <version>${com.howbuy.howbuy-cms-client.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.hibernate.validator</groupId>
                        <artifactId>hibernate-validator</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.howbuy.finonline</groupId>
                <artifactId>fin-online-facade</artifactId>
                <version>${com.howbuy.fin-online-facade.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.fbs</groupId>
                <artifactId>fbs-online-search-facade</artifactId>
                <version>${com.howbuy.fbs-online-search-facade.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>howbuy-framework-rpc</artifactId>
                        <groupId>com.howbuy.pa.framework</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.howbuy.fbs</groupId>
                <artifactId>fbs-online-facade</artifactId>
                <version>${com.howbuy.fbs-online-facade.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.tms</groupId>
                <artifactId>robot-order-center-client</artifactId>
                <version>${com.howbuy.order-center-interaction.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>ftx-online-search-facade</artifactId>
                <version>${com.howbuy.ftx-online-search-facade.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.interlayer</groupId>
                <artifactId>product-center-model</artifactId>
                <version>${com.howbuy.product-center-model.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>*</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-api</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>lombok</artifactId>
                        <groupId>org.projectlombok</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.howbuy.interlayer</groupId>
                <artifactId>product-center-client</artifactId>
                <version>${com.howbuy.product-center-client.version}</version>
            </dependency>

            <dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>elasticsearch-center-client</artifactId>
				<version>${com.howbuy.elasticsearch-center-client.version}</version>
			</dependency>

            <dependency>
                <groupId>com.howbuy.tms</groupId>
                <artifactId>tms-common-cache</artifactId>
                <version>${com.howbuy.tms-common-cache.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>dubbo</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.9</version>
            </dependency>
            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>1.9.3</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk16</artifactId>
                <version>1.46</version>
            </dependency>
            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>2.10.5</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.sharding</groupId>
                <artifactId>howbuy-sharding-id</artifactId>
                <version>${com.howbuy.howbuy-sharding-id.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>spring-boot-starter-web</artifactId>
                        <groupId>org.springframework.boot</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.shardingsphere</groupId>
                <artifactId>shardingsphere-jdbc-core</artifactId>
                <version>5.4.1</version>
                <exclusions>
                    <exclusion>
                        <artifactId>logback-classic</artifactId>
                        <groupId>ch.qos.logback</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.howbuy.tms</groupId>
                <artifactId>tms-common-enums</artifactId>
                <version>${com.howbuy.tms-common-enums.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.tms</groupId>
                <artifactId>tms-common-service</artifactId>
                <version>${com.howbuy.tms-common-service.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>dubbo</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>redis.clients</groupId>
                        <artifactId>jedis</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.commons</groupId>
                        <artifactId>commons-pool2</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.howbuy.tms</groupId>
                <artifactId>tms-common-client</artifactId>
                <version>${com.howbuy.tms-common-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.tms</groupId>
                <artifactId>tms-common-message-service</artifactId>
                <version>${com.howbuy.tms-common-message-service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.tms</groupId>
                <artifactId>message-center-client</artifactId>
                <version>${com.howbuy.message-center-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.tms</groupId>
                <artifactId>order-center-client</artifactId>
                <version>${com.howbuy.order-center-interaction.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.tms</groupId>
                <artifactId>order-center-impl</artifactId>
                <version>${com.howbuy.order-center-interaction.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.tms</groupId>
                <artifactId>order-center-infrastructure</artifactId>
                <version>${com.howbuy.order-center-new.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.tms</groupId>
                <artifactId>order-center-domain</artifactId>
                <version>${com.howbuy.order-center-new.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.tms</groupId>
                <artifactId>order-center-application</artifactId>
                <version>${com.howbuy.order-center-new.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.tms</groupId>
                <artifactId>tms-common-log-pattern</artifactId>
                <version>${com.howbuy.tms-common-log-pattern.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.cc</groupId>
                <artifactId>center-client</artifactId>
                <version>${com.howbuy.center-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.tms</groupId>
                <artifactId>batch-center-client</artifactId>
                <version>${com.howbuy.batch-center.version}</version>
            </dependency>
            <dependency>
                <groupId>org.skyscreamer</groupId>
                <artifactId>jsonassert</artifactId>
                <version>1.5.0</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.tms</groupId>
                <artifactId>high-order-center-client</artifactId>
                <version>${com.howbuy.high-order-center-client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>param-server-facade</artifactId>
                <version>${com.howbuy.param-server-facade.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-core</artifactId>
                <version>${com.alibaba.csp.sentinel-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-transport-simple-http</artifactId>
                <version>${com.alibaba.csp.sentinel-transport-simple-http.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.tms</groupId>
                <artifactId>order-plan-center-model</artifactId>
                <version>${com.howbuy.order-plan-center-model.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.tms</groupId>
                <artifactId>order-plan-center-client</artifactId>
                <version>${com.howbuy.order-plan-center-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.tms</groupId>
                <artifactId>unique-field-processor-tools</artifactId>
                <version>${com.howbuy.unique-field-processor-tools.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <finalName>order-center-new-remote</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        
                        <path>
                            <groupId>com.howbuy.tms</groupId>
                            <artifactId>unique-field-processor-tools</artifactId>
                            <version>${com.howbuy.unique-field-processor-tools.version}</version>
                        </path>
                    </annotationProcessorPaths>
                    <annotationProcessors>
                        <annotationProcessor>com.howbuy.tms.process.UniqueFieldProcessor</annotationProcessor>
                        <annotationProcessor>lombok.launch.AnnotationProcessorHider$AnnotationProcessor</annotationProcessor>
                    </annotationProcessors>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <mainClass>com.howbuy.order.center.demo.OrderCenterDemoApplication</mainClass>
                    <skip>true</skip>
                </configuration>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                
                
                <configuration>
                    <encoding>UTF-8</encoding>
                    <includes>
                        <include>**/*TestM.java</include>
                    </includes>
                    <excludes>
                        <exclude>**/*Test.java</exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-lib</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>target/lib</outputDirectory>
                            <excludeTransitive>false</excludeTransitive>
                            <stripVersion>false</stripVersion>
                            <includeScope>runtime</includeScope>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <distributionManagement>
        <repository>
            <id>howbuy-release</id>
            <name>howbuy-release</name>
            <url>http://nx-java.howbuy.pa/repository/howbuy-release/</url>
        </repository>
        <snapshotRepository>
            <id>howbuy-snapshot</id>
            <name>howbuy-snapshot</name>
            <url>http://nx-java.howbuy.pa/repository/howbuy-snapshot/</url>
        </snapshotRepository>
    </distributionManagement>
</project>