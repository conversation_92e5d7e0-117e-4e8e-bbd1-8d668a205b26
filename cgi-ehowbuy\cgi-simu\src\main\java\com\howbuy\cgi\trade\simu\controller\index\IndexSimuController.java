package com.howbuy.cgi.trade.simu.controller.index;

import com.alibaba.fastjson.JSON;
import com.howbuy.acccenter.facade.query.queryrelatedaccount.bean.RelatedAccountBean;
import com.howbuy.acccenter.facade.query.queryrelatedaccount.bean.SubRelatedAccountBean;
import com.howbuy.cc.center.feature.asset.request.QueryCurrentAssetCertificateStatusRequest;
import com.howbuy.cc.center.feature.asset.response.QueryCurrentAssetCertificateStatusResponse;
import com.howbuy.cc.center.feature.asset.service.QueryCurrentAssetCertificateStatusService;
import com.howbuy.cgi.common.CGIConstants;
import com.howbuy.cgi.common.enums.BizErrorEnum;
import com.howbuy.cgi.common.exception.BizException;
import com.howbuy.cgi.common.remote.param.RemoteParametersProvider;
import com.howbuy.cgi.trade.simu.common.enums.ProductStrategyTypeEnum;
import com.howbuy.cgi.trade.simu.controller.AbstractSimuCGIController;
import com.howbuy.cgi.trade.simu.model.cmd.QueryBalanceParamCmd;
import com.howbuy.cgi.trade.simu.model.cmd.QueryFinReceiptParamCmd;
import com.howbuy.cgi.trade.simu.model.dto.*;
import com.howbuy.cgi.trade.simu.model.vo.SiMuIndexVo;
import com.howbuy.cgi.trade.simu.service.QueryAcctOtherInfoService;
import com.howbuy.cgi.trade.simu.service.QueryBalanceVolService;
import com.howbuy.cgi.trade.simu.service.relatedaccount.AccCenterService;
import com.howbuy.cgi.trade.simu.service.relatedaccount.RelatedAccountService;
import com.howbuy.cgi.trade.simu.util.BusiUtil;
import com.howbuy.cms.service.base.ICmsRecommendService;
import com.howbuy.common.bean.BeanUtils;
import com.howbuy.dtms.order.client.domain.response.hkbalance.dubbo.QueryBalanceResponse;
import com.howbuy.interlayer.common.Constants;
import com.howbuy.interlayer.product.model.*;
import com.howbuy.interlayer.product.service.HighProductService;
import com.howbuy.interlayer.product.service.TradeDayService;
import com.howbuy.kyc.model.KycModel;
import com.howbuy.kyc.service.KycService;
import com.howbuy.persistence.cms.recommended.CmsRecommColumn;
import com.howbuy.persistence.cms.recommended.CmsRecommProduct;
import com.howbuy.simu.dto.base.product.SmxxDto;
import com.howbuy.simu.dto.business.product.SimuRecommendRecord;
import com.howbuy.simu.dto.business.product.SimuZcpzJjInfo;
import com.howbuy.simu.service.base.product.SmZcpzService;
import com.howbuy.simu.service.base.product.SmxxService;
import com.howbuy.simu.service.business.product.SmFundProfileService;
import com.howbuy.tms.common.enums.busi.*;
import com.howbuy.tms.common.enums.database.ShareClassEnum;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceResponse;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceResponse.BalanceBean;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.UnconfirmeProduct;
import com.howbuy.tms.high.orders.facade.search.querybuyfundstatus.QueryBuyFundStatusFacade;
import com.howbuy.tms.high.orders.facade.search.querybuyfundstatus.QueryBuyFundStatusRequest;
import com.howbuy.tms.high.orders.facade.search.querybuyfundstatus.QueryBuyFundStatusResponse;
import com.howbuy.tms.high.orders.facade.search.querybuyfundstatus.QueryBuyFundStatusResponse.BuyFundStatusBean;
import com.howbuy.tms.high.orders.facade.search.queryfinreceipt.QueryFinReceiptResponse;
import com.howbuy.tms.high.orders.facade.search.queryhighfundinvplanlist.QueryHighFundInvPlanListFacade;
import com.howbuy.tms.high.orders.facade.search.queryhighfundinvplanlist.QueryHighFundInvPlanListRequest;
import com.howbuy.tms.high.orders.facade.search.queryhighfundinvplanlist.QueryHighFundInvPlanListResponse;
import com.howbuy.tms.high.orders.facade.search.queryhighfundinvplanlist.bean.HighFundInvPlanVo;
import com.howbuy.tms.high.orders.facade.search.queryredeemfundstatus.QueryRedeemFundStatusFacade;
import com.howbuy.tms.high.orders.facade.search.queryredeemfundstatus.QueryRedeemFundStatusRequest;
import com.howbuy.tms.high.orders.facade.search.queryredeemfundstatus.QueryRedeemFundStatusResponse;
import com.howbuy.tms.high.orders.facade.search.queryredeemfundstatus.QueryRedeemFundStatusResponse.RedeemFundStatusBean;
import com.howbuy.tms.high.orders.facade.search.queryrepurchaseprotocol.QueryCustRepurchaseProtocolFacade;
import com.howbuy.tms.high.orders.facade.search.queryrepurchaseprotocol.QueryCustRepurchaseProtocolRequest;
import com.howbuy.tms.high.orders.facade.search.queryrepurchaseprotocol.QueryCustRepurchaseProtocolResposne;
import com.howbuy.tms.high.orders.facade.search.queryrepurchaseprotocol.bean.CustRepurchaseProtocolBean;
import com.howbuy.trade.account.model.hbone.MemberCustInfoModel;
import com.howbuy.trade.account.service.hbone.HboneService;
import com.howbuy.trade.common.session.model.TradeSession;
import com.howbuy.web.util.WebUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:(高端首页-对内接口(高端交易平台))
 * @reason:
 * @date 2018年6月21日 下午3:23:10
 * @since JDK 1.7
 */
@Controller
public class IndexSimuController extends AbstractSimuCGIController {
    private static final Logger LOG = LogManager.getLogger(IndexSimuController.class);

    @Autowired
    private KycService kycService;
    @Autowired
    @Qualifier("simu.highProductService")
    private HighProductService highProductService;
    @Autowired
    @Qualifier("simu.tradeDayService")
    private TradeDayService tradeDayService;
    @Autowired
    @Qualifier("simu.cmsRecommendService")
    private ICmsRecommendService cmsRecommendService;
    @Autowired
    @Qualifier("simu.smFundProfileService")
    private SmFundProfileService smFundProfileService;
    @Autowired
    @Qualifier("simu.smxxService")
    private SmxxService smxxService;
    @Autowired
    @Qualifier("simu.queryCurrentAssetCertificateStatusService")
    private QueryCurrentAssetCertificateStatusService queryCurrentAssetCertificateStatusService;
    @Autowired
    @Qualifier("simu.queryBuyFundStatusFacade")
    private QueryBuyFundStatusFacade queryBuyFundStatusFacade;
    @Autowired
    @Qualifier("simu.queryRedeemFundStatusFacade")
    private QueryRedeemFundStatusFacade queryRedeemFundStatusFacade;
    @Autowired
    @Qualifier("simu.queryCustRepurchaseProtocolFacade")
    private QueryCustRepurchaseProtocolFacade queryCustRepurchaseProtocolFacade;
    @Autowired
    private RelatedAccountService relatedAccountService;
    @Autowired
    private HboneService hboneService;
    @Autowired
    private QueryAcctOtherInfoService queryAcctOtherInfoService;
    @Autowired
    @Qualifier("simu.queryHighFundInvPlanListFacade")
    private QueryHighFundInvPlanListFacade queryHighFundInvPlanListFacade;
    @Autowired
    private AccCenterService accCenterService;
    @Autowired
    @Qualifier("simu.smZcpzService")
    private SmZcpzService smZcpzService;
    @Autowired
    private QueryBalanceVolService queryBalanceVolService;


    // 默认下单截止时间
    private static final String DEFAULT_TIME = "150000";


    /**
     * @api {GET} /simu/user/balance.htm index
     * @apiVersion 1.0.0
     * @apiGroup IndexSimuController
     * @apiName index
     * @apiDescription 持仓详情页
     * @apiParam {String} [productCode] 产品代码 <br>不传查客户持仓全部产品
     * @apiParam {String} [hkSaleFlag] 好买香港代销标识 <br> 0-否; 1-是; 不传查全部
     * @apiParam {String} [disCode] 销售渠道 HZ000N001:好臻,HB000A001:好买
     * @apiParam {String} [subAccountId] 子关联账户id，查询关联账户资产用
     * @apiSuccess (响应结果) {Number} totalAsset 总资产
     * @apiSuccess (响应结果) {Number} totalUnconfirmedAmt 总待确认金额
     * @apiSuccess (响应结果) {String} isVerifyAssetCertify 是否需要资产证明
     * @apiSuccess (响应结果) {String} verifyStatus 资产证明状态 :0-审核中 ; 1-通过 ;2-未通过 ;3-已过期
     * @apiSuccess (响应结果) {String} isProfessor 是否专业投资者:1- 是; 0- 否
     * @apiSuccess (响应结果) {Number} totalUnconfirmedNum 待确认交易笔数
     * @apiSuccess (响应结果) {Number} totalCurrentAsset 当前总收益
     * @apiSuccess (响应结果) {Number} totalCashCollection 总回款
     * @apiSuccess (响应结果) {String} totalIncomCalStat 总收益计算状态:0-计算中;1-计算成功
     * @apiSuccess (响应结果) {Number} redeemUnconfirmedNum 赎回在途笔数
     * @apiSuccess (响应结果) {String} noTxAcctNo 公募未开户 1 是 0 否
     * @apiSuccess (响应结果) {String} isnologinBind 是否不绑定定登录，用于特定用户不支持微信绑定自登陆
     * @apiSuccess (响应结果) {String} showRelatedAccount 展示关联账户入口 1 是 0 否
     * @apiSuccess (响应结果) {String} relatedCustName 关联客户姓名
     * @apiSuccess (响应结果) {String} relatedHboneNo 关联客户一账通
     * @apiSuccess (响应结果) {String} serverData 服务器日期
     * @apiSuccess (响应结果) {String} highFundInvPlanFlag 是否含有私募定投 0-不含 1-含
     * @apiSuccess (响应结果) {String} hasHZProduct 是否持有好臻产品 0:没有,1:有
     * @apiSuccess (响应结果) {String} hasHKProduct 是否持有好买香港产品  0:没有,1:有
     * @apiSuccess (响应结果) {String} isAuth 是否授权  0:没有,1:有授权
     * @apiSuccess (响应结果) {String} isHkDataQuarantine 是否香港数据隔离,1:是,0:否
     * @apiSuccess (响应结果) {Array} unpaidList 待付款订单
     * @apiSuccess (响应结果) {String} unpaidList.dealNo 订单号
     * @apiSuccess (响应结果) {String} unpaidList.dealType 订单类型
     * @apiSuccess (响应结果) {Array} unconfirmedList 待确认订单
     * @apiSuccess (响应结果) {String} unconfirmedList.dealNo 订单号
     * @apiSuccess (响应结果) {String} unconfirmedList.dealType 订单类型
     * @apiSuccess (响应结果) {Number} buyUnrefundedPiece 购买待退款订单数
     * @apiSuccess (响应结果) {Number} redeemUnrefundedPiece 赎回待回款订单数
     * @apiSuccess (响应结果) {Array} funds 产品维度持仓详情
     * @apiSuccess (响应结果) {String} funds.fundCode 产品编码
     * @apiSuccess (响应结果) {String} funds.fundNameAbbr 产品名称
     * @apiSuccess (响应结果) {String} funds.productType 产品类型
     * @apiSuccess (响应结果) {String} funds.productSubType 产品子类型
     * @apiSuccess (响应结果) {Number} funds.nav 净值
     * @apiSuccess (响应结果) {String} funds.navDate 净值日期
     * @apiSuccess (响应结果) {String} funds.navDivFlag 分红提醒标识,0:无需分红提醒;1:提醒收益偏差;2:提醒即将分红
     * @apiSuccess (响应结果) {Number} funds.totalBala 持仓份额
     * @apiSuccess (响应结果) {Number} funds.unconfirmedVol 待确认份额
     * @apiSuccess (响应结果) {Number} funds.unconfirmedAmt 待确认金额
     * @apiSuccess (响应结果) {Number} funds.fundAsset 市值(人民币)
     * @apiSuccess (响应结果) {Number} funds.currencyMarketValue 市值(当前币种)
     * @apiSuccess (响应结果) {Number} funds.yieldRate 收益率
     * @apiSuccess (响应结果) {Number} funds.currentAsset 当前收益(人民币）
     * @apiSuccess (响应结果) {Number} funds.currentAssetCurrency 当前收益（当前币种）
     * @apiSuccess (响应结果) {String} funds.incomeCalStat 0-计算中；1-计算完成
     * @apiSuccess (响应结果) {String} funds.canBuy 可追加 1.可追加，其他值不可追加
     * @apiSuccess (响应结果) {String} funds.fundBuyStatus 产品购买状态,CAN_BUY:可购买,CAN_NOT_BUY:不可购买,CAN_MODIFY:可修改,CAN_NOT_MODIFY:不可修改;
     * @apiSuccess (响应结果) {String} funds.canRedeem 赎回状态 1.可赎回 ，其他值不可赎回
     * @apiSuccess (响应结果) {String} funds.inDirectBlackList 黑名单
     * @apiSuccess (响应结果) {String} funds.webUrl pc访问地址
     * @apiSuccess (响应结果) {String} funds.cptjly 代销产品推荐
     * @apiSuccess (响应结果) {String} funds.ejflName 基金分类 - 一级分类
     * @apiSuccess (响应结果) {String} funds.openStartDate 开放日开始时间
     * @apiSuccess (响应结果) {String} funds.openEndDate 开放日结束时间
     * @apiSuccess (响应结果) {String} funds.currency 币种
     * @apiSuccess (响应结果) {Number} funds.cashCollection 私募股权回款
     * @apiSuccess (响应结果) {Number} funds.currencyCashCollection 私募股权回款(当前币种)
     * @apiSuccess (响应结果) {Number} funds.currencyNetBuyAmount 净购买金额(投资成本)(当前币种)
     * @apiSuccess (响应结果) {Number} funds.netBuyAmount 净购买金额(投资成本)
     * @apiSuccess (响应结果) {Number} funds.paidInAmt 认缴金额
     * @apiSuccess (响应结果) {Number} funds.paidTotalAmt 实缴金额
     * @apiSuccess (响应结果) {String} funds.paidSubTotalRatio 实缴百分比
     * @apiSuccess (响应结果) {String} funds.fundCXQXStr 产品存续期限(类似于5+3+2这种说明)
     * @apiSuccess (响应结果) {String} funds.StageEstablishFlag 分期成立标识(证券类有此标识:0-否,1-是)
     * @apiSuccess (响应结果) {String} funds.fractionateCallFlag 分次call标识(股权类有此标识:0-否,1-是)
     * @apiSuccess (响应结果) {String} funds.scaleType 销售类型 :1-直销; 2-代销
     * @apiSuccess (响应结果) {String} funds.hkSaleFlag 好买香港代销标识: 0-否; 1-是
     * @apiSuccess (响应结果) {String} funds.rePurchaseFlag 是否复构 0-否 1-是
     * @apiSuccess (响应结果) {String} funds.benchmark 业绩比较基准
     * @apiSuccess (响应结果) {String} funds.valueDate 起息日
     * @apiSuccess (响应结果) {String} funds.dueDate 到期日
     * @apiSuccess (响应结果) {String} funds.standardFixedIncomeFlag 标准固收标识(固收类有此标识:0-否,1-是)
     * @apiSuccess (响应结果) {String} funds.crisisFlag 清盘中标识(清盘中产品有此标识:0-否,1-是)
     * @apiSuccess (响应结果) {Number} funds.yieldIncome 固收货币产品七日年化收益（来自DB）
     * @apiSuccess (响应结果) {String} funds.yieldIncomeDt 固收货币产品净值日期（来自DB）
     * @apiSuccess (响应结果) {String} funds.canModifyRepurchaseProtocolFlag 是否可以修改复购协议(0-否,1-是)
     * @apiSuccess (响应结果) {Object} funds.custRepurchaseProtocol 客户复购协议
     * @apiSuccess (响应结果) {String} funds.custRepurchaseProtocol.txAcctNo 交易账号
     * @apiSuccess (响应结果) {String} funds.custRepurchaseProtocol.repurchaseType 复购类型 0-全部赎回 1-部分复购 2-全部复购
     * @apiSuccess (响应结果) {String} funds.custRepurchaseProtocol.fundCode 产品代码
     * @apiSuccess (响应结果) {String} funds.custRepurchaseProtocol.repurchaseProtocolNo 复购协议号
     * @apiSuccess (响应结果) {Number} funds.custRepurchaseProtocol.repurchaseVol 复购份额
     * @apiSuccess (响应结果) {String} funds.custRepurchaseProtocol.endModifyDt 截止前端日期
     * @apiSuccess (响应结果) {String} funds.custRepurchaseProtocol.canModify 是否可以修改 0-否 1-是
     * @apiSuccess (响应结果) {String} funds.custRepurchaseProtocol.productRepurchaseFlag 产品复购配置标识 <br> 0-否; 1-是
     * @apiSuccess (响应结果) {String} funds.productRepurchaseFlag 产品复购参数配置 0-否 1-是
     * @apiSuccess (响应结果) {String} funds.benchmarkType 业绩比较基准类型（固收类产品有此标识）<br> 0-业绩比较基准（年化） 1-业绩报酬计提基准（年化）
     * @apiSuccess (响应结果) {String} funds.dbFundType DB产品类型
     * @apiSuccess (响应结果) {Number} funds.availVol 可赎回份额
     * @apiSuccess (响应结果) {String} funds.buyAppointStartDt 购买预约开始日期
     * @apiSuccess (响应结果) {String} funds.buyAppointEndDt 赎回预约结束日期
     * @apiSuccess (响应结果) {String} funds.payEndDate 打款截止日期
     * @apiSuccess (响应结果) {String} funds.sellAppointStartDt 赎回预约开始日期
     * @apiSuccess (响应结果) {String} funds.sellAppointEndDt 赎回预约结束日期
     * @apiSuccess (响应结果) {String} funds.productSaleSource 产品销售来源 0-好买 1-海外 2-其他
     * @apiSuccess (响应结果) {Number} funds.lockVol 锁定份额
     * @apiSuccess (响应结果) {String} funds.naProductFeeType NA产品收费类型 10201-好买收费 0-管理人收费
     * @apiSuccess (响应结果) {Number} funds.receivManageFee 累计应收管理费
     * @apiSuccess (响应结果) {Number} funds.receivPreformFee 累计应收业绩报酬
     * @apiSuccess (响应结果) {Number} funds.currencyMarketValueExFee NA产品费后市值(当前币种， 减去当前累计应收管理费和累计应收业绩报酬)
     * @apiSuccess (响应结果) {Number} funds.marketValueExFee NA产品费后市值(人民币， 减去当前累计应收管理费和累计应收业绩报酬)
     * @apiSuccess (响应结果) {String} funds.gaoYiLingShanFlag 是否高毅领山产品 0-否 1-是
     * @apiSuccess (响应结果) {Number} funds.balanceFactor 平衡因子
     * @apiSuccess (响应结果) {String} funds.convertFinish 平衡因子转换完成 1-是 0-否
     * @apiSuccess (响应结果) {String} funds.balanceFactorDate 平衡因子日期
     * @apiSuccess (响应结果) {String} funds.ownershipTransferIdentity 股权转让标识
     * @apiSuccess (响应结果) {String} funds.strategy 产品策略
     * @apiSuccess (响应结果) {String} funds.strategyStr 产品策略中文描述
     * @apiSuccess (响应结果) {String} funds.navStr ********************字段String处理 start
     * @apiSuccess (响应结果) {String} funds.totalBalaStr 持仓份额
     * @apiSuccess (响应结果) {String} funds.unconfirmedVolStr 待确认额度
     * @apiSuccess (响应结果) {String} funds.unconfirmedAmtStr 待确认金额
     * @apiSuccess (响应结果) {String} funds.fundAssetStr
     * @apiSuccess (响应结果) {String} funds.currencyMarketValueStr
     * @apiSuccess (响应结果) {String} funds.yieldRateStr
     * @apiSuccess (响应结果) {String} funds.currentAssetStr
     * @apiSuccess (响应结果) {String} funds.currentAssetCurrencyStr
     * @apiSuccess (响应结果) {String} funds.cashCollectionStr
     * @apiSuccess (响应结果) {String} funds.currencyCashCollectionStr
     * @apiSuccess (响应结果) {String} funds.currencyNetBuyAmountStr
     * @apiSuccess (响应结果) {String} funds.netBuyAmountStr
     * @apiSuccess (响应结果) {String} funds.paidInAmtStr
     * @apiSuccess (响应结果) {String} funds.receivManageFeeStr
     * @apiSuccess (响应结果) {String} funds.receivPreformFeeStr
     * @apiSuccess (响应结果) {String} funds.currencyMarketValueExFeeStr
     * @apiSuccess (响应结果) {String} funds.marketValueExFeeStr
     * @apiSuccess (响应结果) {String} funds.cpqxsm 股权产品期限说明
     * @apiSuccess (响应结果) {String} funds.subProductCode 子产品代码
     * @apiSuccess (响应结果) {String} funds.establishDt 产品成立日期
     * @apiSuccess (响应结果) {String} funds.sfhwcxg 是否海外储蓄罐(1:是;0:否)
     * @apiSuccess (响应结果) {Number} funds.copiesIncome 万份收益--持仓特殊产品指标控制需求新增 20221122
     * @apiSuccess (响应结果) {String} funds.navDisclosureType 净值披露方式(1-净值,2-份额收益)--持仓特殊产品指标控制需求新增 20221122
     * @apiSuccess (响应结果) {String} funds.abnormalFlag 对于 阳光私募/非货基型券商集合 产品，若【最新净值日期】比第一笔交易的【确认日期】早3个月以上      异常标志(0-否 1-是)--持仓特殊产品指标控制需求新增 20221122
     * @apiSuccess (响应结果) {String} funds.fundAssetCtl 人民币市值-是否控制表人为置空
     * @apiSuccess (响应结果) {String} funds.currencyMarketValueCtl 当前币种市值-是否控制表人为置空
     * @apiSuccess (响应结果) {String} funds.currencyMarketValueExFeeCtl NA产品费后市值（当前币种）-是否控制表人为置空
     * @apiSuccess (响应结果) {String} funds.marketValueExFeeCtl NA产品费后市值（人民币）-是否控制表人为置空
     * @apiSuccess (响应结果) {String} funds.currentAssetCtl 人民币收益-是否控制表人为置空
     * @apiSuccess (响应结果) {String} funds.currentAssetCurrencyCtl 当前币种收益-是否控制表人为置空
     * @apiSuccess (响应结果) {String} funds.yieldRateCtl 收益率-是否控制表人为置空
     * @apiSuccess (响应结果) {String} funds.totalBalaCtl 份额-是否控制表人为置空
     * @apiSuccess (响应结果) {String} funds.unconfirmedVolCtl 待确认份额-是否控制表人为置空
     * @apiSuccess (响应结果) {String} funds.navCtl 净值-是否控制表人为置空
     * @apiSuccess (响应结果) {String} funds.stageFlag 是否拆单产品 1-是
     * @apiSuccess (响应结果) {String} funds.qianXiFlag 是否为千禧年产品 0-否、1-是
     * @apiSuccess (响应结果) {Number} funds.unPaidInAmt 待投金额（人民币）
     * @apiSuccess (响应结果) {Number} funds.currencyUnPaidInAmt 待投金额（当前币种）
     * @apiSuccess (响应结果) {String} funds.disCode 分销机构编码
     * @apiSuccess (响应结果) {Number} funds.dayIncome 产品最新收益
     * @apiSuccess (响应结果) {Number} funds.dayIncomeRate 最新收益率
     * @apiSuccess (响应结果) {String} funds.incomeLatestDay 最新收益日期
     * @apiSuccess (响应结果) {Number} funds.accumIncome 产品累计收益
     * @apiSuccess (响应结果) {Array} unconfirmedFunds 待确认
     * @apiSuccess (响应结果) {String} unconfirmedFunds.fundCode 产品代码
     * @apiSuccess (响应结果) {String} unconfirmedFunds.productType 产品类型
     * @apiSuccess (响应结果) {String} unconfirmedFunds.disCode 销售渠道
     * @apiSuccess (响应结果) {String} unconfirmedFunds.productSubType 产品子类型
     * @apiSuccess (响应结果) {Number} unconfirmedFunds.unconfirmedAmt 待确认金额(人民币)
     * @apiSuccess (响应结果) {String} unconfirmedFunds.hkSaleFlag 好买香港代销标识: 0-否; 1-是
     * @apiSuccessExample 响应结果示例
     * {"totalAsset":9767.69,"highFundInvPlanFlag":"InMCM","relatedCustName":"rte","redeemUnrefundedPiece":4960,"isVerifyAssetCertify":"YLp54NIYe","unconfirmedList":[{"dealNo":"Tq","dealType":"RxEe"}],"isAuth":"ZprUC","verifyStatus":"7qm28Tp4WD","isHkDataQuarantine":"9a0q","buyUnrefundedPiece":3271,"funds":[{"ejflName":"dEy","unconfirmedVolStr":"t7CC9rQL","currencyCashCollection":5506.142128975962,"disCode":"mHwyhEgs","cashCollectionStr":"QOYonWvtk1","productSubType":"F8w52k","openEndDate":"ofAt","currentAsset":3920.9427416745357,"fundCode":"W4","cashCollection":3171.7346191474317,"custRepurchaseProtocol":{"repurchaseVol":2707.066984818726,"productRepurchaseFlag":"RiCxIVrzJ","fundCode":"KT8iF","repurchaseProtocolNo":"Jv9FUhtoV6","repurchaseType":"LARo","txAcctNo":"zRM","endModifyDt":"EdujbbyRl","canModify":"dUXKU12DQo"},"cptjly":"I1z","nav":6944.293478313507,"buyAppointStartDt":"aQ9o9IJp","abnormalFlag":"4oh4KjNe","unconfirmedVolCtl":"frV1Iw5hGz","benchmark":"ANM2bc","dbFundType":"4BePvT","gaoYiLingShanFlag":"L","fundAssetStr":"bWChoeJhJ","netBuyAmount":3088.2603667090125,"unconfirmedAmtStr":"b","fundBuyStatus":"Vf","netBuyAmountStr":"GyS","buyAppointEndDt":"oW","currencyMarketValueExFee":7948.136674570043,"navStr":"eMLtWj","unPaidInAmt":4820.8112121678,"marketValueExFeeCtl":"cp6","establishDt":"6PEr6YqIUj","currencyMarketValue":7843.214198556102,"productSaleSource":"mp","navDisclosureType":"6TVdnP","currency":"wJr24psK","balanceFactor":5944.977302921668,"sellAppointStartDt":"TA","fundNameAbbr":"H","standardFixedIncomeFlag":"z4rFl","yieldIncome":1625.2033757305162,"navDate":"nZC","totalBalaStr":"QObtyvDFc","dayIncomeRate":4960.964781719672,"receivPreformFeeStr":"zppIT","paidSubTotalRatio":"EX","naProductFeeType":"T2WcY","qianXiFlag":"Dol9f","incomeCalStat":"891t","yieldRate":4761.949737492086,"currencyNetBuyAmountStr":"tG","paidInAmt":7005.703798157552,"currentAssetCurrency":1614.3079020809303,"copiesIncome":1596.031877169557,"totalBalaCtl":"kHgQAASUZD","payEndDate":"U","crisisFlag":"IKe2AgOS7j","dueDate":"hyc","lockVol":2091.400780969037,"yieldRateStr":"2TBqgF","marketValueExFee":1536.3039529343114,"productRepurchaseFlag":"uOGh1","availVol":9847.619443769298,"StageEstablishFlag":"Nk","fundAsset":8688.706864727612,"receivPreformFee":2692.8226049624436,"accumIncome":9984.647609809701,"yieldRateCtl":"eVsww","currentAssetStr":"FBqRd2g9qN","currencyNetBuyAmount":6343.807381903276,"currencyUnPaidInAmt":4646.197525550816,"currencyMarketValueExFeeStr":"YQ1G5gnfx","rePurchaseFlag":"ut","ownershipTransferIdentity":"rrACYjd7V","scaleType":"ZUN2ht","webUrl":"hJf3J7aS","currentAssetCtl":"S54Z","currencyMarketValueCtl":"Oe","navCtl":"HD","paidTotalAmt":9583.615278415336,"marketValueExFeeStr":"PzKJ","benchmarkType":"Un","currentAssetCurrencyStr":"bjdV","balanceFactorDate":"FRjoHv7","fundAssetCtl":"MYIN6tQ","currencyMarketValueExFeeCtl":"RGE95Ei","paidInAmtStr":"WxC6Y","navDivFlag":"WSUarXIQLz","fundCXQXStr":"CtfeT6j2","dayIncome":2966.1812407362086,"totalBala":951.5953628923323,"openStartDate":"EUB","yieldIncomeDt":"n9m","hkSaleFlag":"kpWF3J3","sfhwcxg":"ZxPRQ","incomeLatestDay":"Yp0weS6eA","sellAppointEndDt":"sqeDNM","currencyMarketValueStr":"HWVy","productType":"8zVl","strategyStr":"fZ5","cpqxsm":"e","subProductCode":"UW6","canModifyRepurchaseProtocolFlag":"MjfRJEGSKL","valueDate":"XsW","receivManageFee":8836.138985967971,"canRedeem":"1v","canBuy":"TYHtQ2OF","unconfirmedVol":5079.074933383361,"receivManageFeeStr":"31LPvFUQIp","inDirectBlackList":"oXyWF","unconfirmedAmt":416.66697991566525,"fractionateCallFlag":"9X9D8nL","currentAssetCurrencyCtl":"qaxx","strategy":"LhAZ","stageFlag":"z6j7nbucr","currencyCashCollectionStr":"g7","convertFinish":"h6sopza"}],"hasHKProduct":"H","isnologinBind":"th0Ybw","hasHZProduct":"LbO9","totalUnconfirmedAmt":2932.82,"totalUnconfirmedNum":6335,"totalCashCollection":3677.*************,"unpaidList":[{"dealNo":"RwQZP","dealType":"NcKwU"}],"serverData":"U","noTxAcctNo":"odnX9RvQz","relatedHboneNo":"es","unconfirmedFunds":[{"fundCode":"Iw2zn76B8","unconfirmedAmt":6771.************,"disCode":"Iof","hkSaleFlag":"AYD9BN","productSubType":"zanNT","productType":"flSl"}],"showRelatedAccount":"6ML","totalCurrentAsset":8460.************,"isProfessor":"WZmbUedA3I","totalIncomCalStat":"Mtngh33","redeemUnconfirmedNum":9260}
     */
    @Deprecated
    @RequestMapping("/simu/user/balance.htm")
    public void index(HttpServletRequest request, HttpServletResponse response) throws Exception {
        TradeSession loginInfo = this.getCustSession();
        IndexDTO rtnDTO = new IndexDTO();
        rtnDTO.setServerData(DateUtils.formatToString(new Date(), DateUtils.YYYYMMDD));
        if (null == loginInfo || null == loginInfo.getUser()) {
            LOG.info("loginInfo is null!");
            throw new BizException(BizErrorEnum.NEED_RELOGIN.getCode(), BizErrorEnum.NEED_RELOGIN.getDesc());
        }

        // 查询高端用户持仓信息
        String disCode = getString("disCode");
        //香港代销标识
        String hkSaleFlag = getString("hkSaleFlag");
        //是否查询海外持仓的时候,用海外接口
        String queryWithHkInterface = getString("queryWithHkInterface");
        // 默认是使用海外接口查询海外持仓
        if (StringUtils.isBlank(queryWithHkInterface)) {
            queryWithHkInterface = YesOrNoEnum.YES.getCode();
        }
        //产品代码
        String productCode = getString("productCode");
        //产品类型
        String productType = getString("productType");
        //产品子类型
        String productSubType = getString("productSubType");
        String ip = WebUtil.getCustIP(request);
        // 子关联账户id
        String subAccountId = getString("subAccountId");
        String hboneNo = loginInfo.getUser().getHboneNo();
        if (StringUtils.isBlank(subAccountId)) {
            // 查询个人首页
            String txAcctNo = loginInfo.getUser().getTxAcctNo();
            checkIsZcvipBindWhite(rtnDTO, hboneNo);
            // 客户资产证明/Kyc信息处理
            processAssetCertificateAndKyc(hboneNo, rtnDTO);
            // 是否展示关联账户入口
            String showRelatedAccount = relatedAccountService.showRelatedAccount(hboneNo, txAcctNo);
            rtnDTO.setShowRelatedAccount(showRelatedAccount);
            // 查询是否授权
            AcctDataAuthDto acctDataAuthInfo = accCenterService.getAcctDataAuthInfo(hboneNo);
            rtnDTO.setIsAuth(acctDataAuthInfo.getIsDataAuth());
            rtnDTO.setIsHkDataQuarantine(acctDataAuthInfo.getIsHkDataQuarantine());
            // 查询当前账户持仓
            queryBalance(rtnDTO, txAcctNo, hboneNo, hkSaleFlag, productType, productSubType, productCode, ip, disCode, false, response, queryWithHkInterface);
        } else {
            String subHboneNo = getSubHbOneNo(hboneNo, subAccountId);
            rtnDTO.setRelatedHboneNo(getEncHboneNo(subHboneNo));
            // 查询关联账户客户信息
            MemberCustInfoModel custInfo = hboneService.queryCustInfoByHboneNo(subHboneNo);
            if (custInfo == null) {
                log.error("查询关联账户客户信息失败");
                throw new BizException(BizErrorEnum.SYSTEM_ERROR.getCode(), BizErrorEnum.SYSTEM_ERROR.getDesc());
            }
            rtnDTO.setRelatedCustName(custInfo.getCustName());

            // 查询是否授权
            AcctDataAuthDto acctDataAuthInfo = accCenterService.getAcctDataAuthInfo(subHboneNo);
            rtnDTO.setIsAuth(acctDataAuthInfo.getIsDataAuth());
            rtnDTO.setIsHkDataQuarantine(acctDataAuthInfo.getIsHkDataQuarantine());
            // 查询关联账户持仓
            queryBalance(rtnDTO, custInfo.getCustNo(), subHboneNo, hkSaleFlag, productType, productSubType, productCode, ip, disCode, true, response, queryWithHkInterface);
        }
    }


    /**
     * @api {GET} /simu/user/balanceV2.htm indexV2
     * @apiVersion 1.0.0
     * @apiGroup IndexSimuController
     * @apiName indexV2
     * @apiDescription 私募持仓首页V2
     * @apiParam (请求参数) {String} disCode 分销渠道
     * @apiParam (请求参数) {String} hkSaleFlag 是否只查询香港标识,1:是,0:否
     * @apiParam (请求参数) {String} productCode 产品代码
     * @apiParam (请求参数) {String} productType 产品类型,7-一对多专户,11-私募
     * @apiParam (请求参数) {String} productSubType 产品二级类型:0-现金管理,1-债券,2-类固定收益,3-对冲策略,4-股票策略,5-股权,6-房地产基金,7-其他
     * @apiParam (请求参数) {String} subAccountId 子关联账户id
     * @apiParam (请求参数) {String} hboneNo 一账通号
     * @apiParamExample 请求参数示例
     * productCode=Ll&subAccountId=Bv8Wj&disCode=U&hkSaleFlag=x07&productSubType=kIDdy&productType=TZhezKpq&hboneNo=It3GAEe
     * @apiSuccess (响应结果) {Number} totalAsset 总资产
     * @apiSuccess (响应结果) {String} abnormalState 是否存在异常,持仓中只要有一笔存在异常，总资产就需显示计算中标识;1:是,0:否
     * @apiSuccess (响应结果) {String} totalIncomCalStat 总收益计算状态:0-计算中;1-计算成功
     * @apiSuccess (响应结果) {String} showRelatedAccount 展示关联账户入口 1 是 0 否
     * @apiSuccess (响应结果) {String} relatedCustName 关联客户姓名
     * @apiSuccess (响应结果) {String} relatedHboneNo 关联客户一账通
     * @apiSuccess (响应结果) {Number} totalUnconfirmedAmt 总待确认金额
     * @apiSuccess (响应结果) {String} isNeedAuth 是否需要授权:1- 是; 0- 否
     * @apiSuccess (响应结果) {Number} totalCurrentAsset 当前总收益
     * @apiSuccess (响应结果) {String} isProfessor 是否专业投资者:1- 是; 0- 否
     * @apiSuccess (响应结果) {String} noTxAcctNo 公募未开户 1 是 0 否
     * @apiSuccess (响应结果) {Number} balanceNum 持仓条数,1个母基金下有2子基金,算2条
     * @apiSuccess (响应结果) {String} onWayMsg 在途列表文案
     * @apiSuccess (响应结果) {Array} onWayDealVoList 在途订单号列表
     * @apiSuccess (响应结果) {String} onWayDealVoList.dealNo 订单号
     * @apiSuccess (响应结果) {String} onWayDealVoList.dealType 订单类型：0-直销、1-代销
     * @apiSuccess (响应结果) {String} fundArrivalMsg 资金到账提醒文案
     * @apiSuccess (响应结果) {String} isDataAuth 是否签署数据授权,1:已授权,0:没有授权
     * @apiSuccess (响应结果) {String} isnologinBind 是否不绑定定登录，用于特定用户不支持微信绑定自登陆,1:不绑定自登陆,0:绑定自登陆
     * @apiSuccess (响应结果) {Number} waitSupSignAgreementNum 补签协议条数
     * @apiSuccess (响应结果) {String} existPassword 是否存在密码,1:是,0:否
     * @apiSuccess (响应结果) {String} highFundInvPlanFlag 是否含有私募定投 1:是,0:否
     * @apiSuccess (响应结果) {Array} waitSupSignAgreementFundList 补签协议产品信息
     * @apiSuccess (响应结果) {String} waitSupSignAgreementFundList.fundCode 产品编码
     * @apiSuccess (响应结果) {String} waitSupSignAgreementFundList.fundName 产品名称
     * @apiSuccess (响应结果) {String} waitSupSignAgreementFundList.disCode 分销编码
     * @apiSuccess (响应结果) {String} waitSupSignAgreementFundList.hkSaleFlag 是否香港产品,1:是;0:不是
     * @apiSuccess (响应结果) {String} showShouYi 是否展示收益分析入口
     * @apiSuccess (响应结果) {String} showLiCai 是否展示理财分析入口
     * @apiSuccess (响应结果) {Array} fundSetVoList 产品集合信息
     * @apiSuccess (响应结果) {Number} fundSetVoList.totalAsset 资产合计(元)
     * @apiSuccess (响应结果) {String} fundSetVoList.fundSetType 产品集合类型
     * @apiSuccess (响应结果) {Number} fundSetVoList.balanceNum 持仓笔数
     * @apiSuccess (响应结果) {Number} fundSetVoList.sort 分类排序
     * @apiSuccess (响应结果) {Array} fundSetVoList.fundBalanceVoList 产品持仓信息
     * @apiSuccess (响应结果) {String} fundSetVoList.fundBalanceVoList.fundCode 产品代码
     * @apiSuccess (响应结果) {String} fundSetVoList.fundBalanceVoList.fundName 产品名称
     * @apiSuccess (响应结果) {String} fundSetVoList.fundBalanceVoList.stageEstablishFlag 是否分期成立,1-是;0-否
     * @apiSuccess (响应结果) {Number} fundSetVoList.fundBalanceVoList.currentAssetCurrency 当前收益（当前币种）
     * @apiSuccess (响应结果) {Number} fundSetVoList.fundBalanceVoList.currentAsset 当前收益（人民币）
     * @apiSuccess (响应结果) {Number} fundSetVoList.fundBalanceVoList.totalAsset 总市值(人民币)
     * @apiSuccess (响应结果) {Number} fundSetVoList.fundBalanceVoList.balanceNum 持仓笔数
     * @apiSuccess (响应结果) {Number} fundSetVoList.fundBalanceVoList.totalBalanceVol 总份额
     * @apiSuccess (响应结果) {Number} fundSetVoList.fundBalanceVoList.cashCollection 私募股权回款金额
     * @apiSuccess (响应结果) {String} fundSetVoList.fundBalanceVoList.cashCollectionStr 私募股权回款金额String
     * @apiSuccess (响应结果) {Number} fundSetVoList.fundBalanceVoList.currencyMarketValue 总市值(当前币种)
     * @apiSuccess (响应结果) {String} fundSetVoList.fundBalanceVoList.isShowYieldRate 是否显示收益率 1 是 0 否
     * @apiSuccess (响应结果) {String} fundSetVoList.fundBalanceVoList.fundSetType 产品集合类型
     * @apiSuccess (响应结果) {String} fundSetVoList.fundBalanceVoList.navDivFlag 净值分红标识,0-否，1-是
     * @apiSuccess (响应结果) {String} fundSetVoList.fundBalanceVoList.productType 产品类型,7-一对多专户,11-私募
     * @apiSuccess (响应结果) {String} fundSetVoList.fundBalanceVoList.productSubType 产品二级类型:0-现金管理,1-债券,2-类固定收益,3-对冲策略,4-股票策略,5-股权,6-房地产基金,7-其他
     * @apiSuccess (响应结果) {String} fundSetVoList.fundBalanceVoList.abnormalState 异常标识	0-否; 1-是
     * @apiSuccess (响应结果) {String} fundSetVoList.fundBalanceVoList.crisisFlag 清算标识    0-否; 1-是
     * @apiSuccess (响应结果) {String} fundSetVoList.fundBalanceVoList.qianXiFlag 千禧年待投产品标识	0-否; 1-是
     * @apiSuccess (响应结果) {Number} fundSetVoList.fundBalanceVoList.marketValue 总市值(人民币)
     * @apiSuccess (响应结果) {Number} fundSetVoList.fundBalanceVoList.yieldRate 当前收益率
     * @apiSuccess (响应结果) {String} fundSetVoList.fundBalanceVoList.nav 净值
     * @apiSuccess (响应结果) {String} fundSetVoList.fundBalanceVoList.navDate 净值更新时间,YYYYMMDD或者MMDD
     * @apiSuccess (响应结果) {String} fundSetVoList.fundBalanceVoList.navDisclosureType 净值披露方式,1-净值,2-份额收益
     * @apiSuccess (响应结果) {String} fundSetVoList.fundBalanceVoList.incomeCalStat 收益计算状态 0-计算中;1-计算成功
     * @apiSuccess (响应结果) {String} fundSetVoList.fundBalanceVoList.currencyCode 币种
     * @apiSuccess (响应结果) {String} fundSetVoList.fundBalanceVoList.currencyDesc 币种描述
     * @apiSuccess (响应结果) {String} fundSetVoList.fundBalanceVoList.standardFixedIncomeFlag 标准固收标识（固收类产品有此标识）,0-非标准固收，1-标准固收，2-现金管理， 3-券商集合理财 4-纯债产品
     * @apiSuccess (响应结果) {String} fundSetVoList.fundBalanceVoList.dueDate 到期日
     * @apiSuccess (响应结果) {Number} fundSetVoList.fundBalanceVoList.yieldIncome 固收货币产品七日年化收益（来自DB）
     * @apiSuccess (响应结果) {String} fundSetVoList.fundBalanceVoList.productDuration 产品存续期限(类似于5+3+2这种说明)
     * @apiSuccess (响应结果) {Number} fundSetVoList.fundBalanceVoList.currencyCashCollection 私募股权回款(当前币种)
     * @apiSuccess (响应结果) {String} fundSetVoList.fundBalanceVoList.cpqxsm 产品期限说明
     * @apiSuccess (响应结果) {Number} fundSetVoList.fundBalanceVoList.unPaidInAmt 待投金额（人民币）
     * @apiSuccess (响应结果) {Number} fundSetVoList.fundBalanceVoList.paidInAmt 总实缴金额
     * @apiSuccess (响应结果) {Number} fundSetVoList.fundBalanceVoList.currencyUnPaidInAmt 待投金额（当前币种）
     * @apiSuccess (响应结果) {Number} fundSetVoList.fundBalanceVoList.currencyNetBuyAmount 净购买金额(投资成本)(当前币种)
     * @apiSuccess (响应结果) {String} fundSetVoList.fundBalanceVoList.disCode 产品渠道
     * @apiSuccess (响应结果) {String} fundSetVoList.fundBalanceVoList.hkSaleFlag 好买香港代销标识: 0-否; 1-是
     * @apiSuccess (响应结果) {String} fundSetVoList.fundBalanceVoList.yieldIncomeDt 固收货币产品净值日期（来自DB）
     * @apiSuccess (响应结果) {Number} fundSetVoList.fundBalanceVoList.sort 为了保持跟老接口返回产品顺序一致,加的产品原排序
     * @apiSuccess (响应结果) {Array} fundSetVoList.fundBalanceVoList.fundItemVoList 产品详细信息(如果是分期成立,则会有多条)
     * @apiSuccessExample 响应结果示例
     * {"isNeedAuth":"LBB","fundSetVoList":[{"totalAsset":7041.5330688861195,"fundSetType":"2E7vTiv","fundBalanceVoList":[{"totalAsset":6458.716425906034,"isShowYieldRate":"Sh8F8b","crisisFlag":"w","abnormalState":"ZU1DxFuih","navDivFlag":"i91jKC","dueDate":"oQ92am","currencyCashCollection":5959.693571992155,"totalBalanceVol":7751.712513991249,"disCode":"0pRxRg","cashCollectionStr":"C","productSubType":"UVGqr5s4","unPaidInAmt":7738.084608384656,"currentAsset":2732.1896578612227,"balanceNum":383,"currencyMarketValue":8804.222522401185,"fundCode":"YfCbUOQ","cashCollection":2874.1987245720024,"navDisclosureType":"Aj4WA8","yieldIncomeDt":"6d3Bn4D","hkSaleFlag":"e3Hdk8K","productType":"JF9J","standardFixedIncomeFlag":"evdd","fundSetType":"lJfnRXDV","currencyDesc":"qGF","cpqxsm":"oRExf","nav":"8Q03hiMXBe","yieldIncome":4465.110271456941,"navDate":"7q","productDuration":"pw3u","marketValue":564.2317123480523,"currencyUnPaidInAmt":4624.533499427815,"currencyNetBuyAmount":155.9356531898226,"sort":143,"qianXiFlag":"n4v9g","incomeCalStat":"1wdemqJ3a","yieldRate":529.5123124436751,"fundItemVoList":[],"paidInAmt":8869.853382948724,"fundName":"94PepuQQw","currentAssetCurrency":9146.***********,"currencyCode":"r","stageEstablishFlag":"Ju"}],"balanceNum":5060,"sort":942}],"totalAsset":9265.***********,"abnormalState":"J1vtTeCi","totalUnconfirmedAmt":4283.************,"onWayMsg":"2zYcw","highFundInvPlanFlag":"q","onWayDealVoList":[{"dealNo":"20","dealType":"UfzuRCz5nS"}],"relatedCustName":"aBuXW","showShouYi":"jiEGd","showLiCai":"I","fundArrivalMsg":"tb","isDataAuth":"mtxhRrnrE","noTxAcctNo":"nHZeq","waitSupSignAgreementFundList":[{"fundCode":"LR","disCode":"vQei7ZY","fundName":"tP","hkSaleFlag":"6QSrkfX7"}],"relatedHboneNo":"Q","balanceNum":8378,"waitSupSignAgreementNum":7277,"showRelatedAccount":"TLNHVqyitH","existPassword":"F","totalCurrentAsset":8021.************,"isProfessor":"g5Eri7u","isnologinBind":"36K3DppJ6U","totalIncomCalStat":"66Vr2QRz"}
     */
    @RequestMapping("/simu/user/balanceV2.htm")
    public void indexV2(HttpServletRequest request, HttpServletResponse response) throws Exception {
        TradeSession loginInfo = this.getCustSession();
        if (null == loginInfo || null == loginInfo.getUser()) {
            LOG.info("loginInfo is null!");
            throw new BizException(BizErrorEnum.NEED_RELOGIN.getCode(), BizErrorEnum.NEED_RELOGIN.getDesc());
        }
        QueryBalanceParamCmd queryBalanceParamCmd = buildQueryBalanceParam(request);
        SiMuIndexVo siMuIndexVo = queryBalanceVolService.queryIndexBalanceInfo(queryBalanceParamCmd);
        write(siMuIndexVo, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }

    /**
     * 构建查询持仓参数
     */
    private QueryBalanceParamCmd buildQueryBalanceParam(HttpServletRequest request) {
        QueryBalanceParamCmd queryBalanceParamCmd = new QueryBalanceParamCmd();
        // 查询高端用户持仓信息
        queryBalanceParamCmd.setDisCode(getString("disCode"));
        // 香港代销标识
        queryBalanceParamCmd.setHkSaleFlag(getString("hkSaleFlag"));
        // 产品代码
        queryBalanceParamCmd.setProductCode(getString("productCode"));
        // 产品类型
        queryBalanceParamCmd.setProductType(getString("productType"));
        // 产品子类型
        queryBalanceParamCmd.setProductSubType(getString("productSubType"));
        // ip
        queryBalanceParamCmd.setIp(WebUtil.getCustIP(request));
        // 子关联账户id
        queryBalanceParamCmd.setSubAccountId(getString("subAccountId"));
        // 一账通
        TradeSession loginInfo = this.getCustSession();
        // 如果关联子账户id不为空,那么一账通就用关联子账号的
        String subHbOneNo = null;
        if (StringUtils.isNotBlank(queryBalanceParamCmd.getSubAccountId())) {
            subHbOneNo = getSubHbOneNo(loginInfo.getUser().getHboneNo(), queryBalanceParamCmd.getSubAccountId());
            // 关联账户一账通
            queryBalanceParamCmd.setRelatedHboneNo(getEncHboneNo(subHbOneNo));
        }
        if (StringUtils.isNotBlank(subHbOneNo)) {
            queryBalanceParamCmd.setHboneNo(subHbOneNo);
        } else {
            queryBalanceParamCmd.setHboneNo(loginInfo.getUser().getHboneNo());
        }
        // 交易账号,账户客户信息
        if (StringUtils.isNotBlank(queryBalanceParamCmd.getSubAccountId()) || StringUtils.isBlank(loginInfo.getUser().getTxAcctNo())) {
            MemberCustInfoModel custInfo = hboneService.queryCustInfoByHboneNo(queryBalanceParamCmd.getHboneNo());
            if (custInfo == null) {
                log.error("IndexSimuController-查询关联账户客户信息失败,hBoneNo={}", queryBalanceParamCmd.getHboneNo());
                throw new BizException(BizErrorEnum.SYSTEM_ERROR.getCode(), BizErrorEnum.SYSTEM_ERROR.getDesc());
            }
            queryBalanceParamCmd.setTxAcctNo(custInfo.getCustNo());
            queryBalanceParamCmd.setCustName(custInfo.getCustName());
        } else {
            queryBalanceParamCmd.setTxAcctNo(loginInfo.getUser().getTxAcctNo());
            queryBalanceParamCmd.setCustName(loginInfo.getUser().getCustName());
        }
        // 默认按照是否数据授权查询
        AcctDataAuthDto acctDataAuthInfo = accCenterService.getAcctDataAuthInfo(queryBalanceParamCmd.getHboneNo());
        if (YesOrNoEnum.YES.getCode().equals(acctDataAuthInfo.getIsDataAuth())) {
            queryBalanceParamCmd.setNotFilterHkFund(YesOrNoEnum.YES.getCode());
            queryBalanceParamCmd.setNotFilterHzFund(YesOrNoEnum.YES.getCode());
            // 如果香港数据隔离,就过滤香港的产品
            if (YesOrNoEnum.YES.getCode().equals(acctDataAuthInfo.getIsHkDataQuarantine())) {
                queryBalanceParamCmd.setNotFilterHkFund(YesOrNoEnum.NO.getCode());
            }
        } else {
            queryBalanceParamCmd.setNotFilterHkFund(YesOrNoEnum.NO.getCode());
            queryBalanceParamCmd.setNotFilterHzFund(YesOrNoEnum.NO.getCode());
        }
        queryBalanceParamCmd.setAcctDataAuthInfo(acctDataAuthInfo);
        return queryBalanceParamCmd;
    }

    /**
     * 获取关联子账户对应的一账通号
     */
    private String getSubHbOneNo(String hBoneNo, String subAccountId) {
        if (StringUtils.isBlank(subAccountId) || StringUtils.isBlank(hBoneNo)) {
            return null;
        }
        // 查询当前账户的关联账户
        List<RelatedAccountBean> accountList = relatedAccountService.queryRelatedAccount(hBoneNo, null, null);
        if (accountList == null) {
            log.error("IndexSimuController-当前客户不存在关联账户,hBoneNo={}", hBoneNo);
            throw new BizException(BizErrorEnum.SYSTEM_ERROR.getCode(), BizErrorEnum.SYSTEM_ERROR.getDesc());
        }
        for (RelatedAccountBean account : accountList) {
            if (account.getSubRelatedAccountList() != null) {
                for (SubRelatedAccountBean subAccount : account.getSubRelatedAccountList()) {
                    if (subAccountId.equals(subAccount.getSubRelatedAccountId())) {
                        return subAccount.getSubHboneNo();

                    }
                }
            }
        }
        log.error("IndexSimuController-该关联账户id不是当前客户的子账户,hBoneNo={},subAccountId={}", hBoneNo, subAccountId);
        throw new BizException(BizErrorEnum.SYSTEM_ERROR.getCode(), BizErrorEnum.SYSTEM_ERROR.getDesc());
    }

    /**
     * 查询持仓详情信息
     */
    private void queryBalance(IndexDTO rtnDTO, String txAcctNo, String hbOneNo,
                              String hkSaleFlag, String productType, String productSubType,
                              String productCode, String ip, String disCode,
                              boolean isSubAccount, HttpServletResponse response, String queryWithHkInterface) throws IOException {
        QueryBalanceParamCmd queryBalanceParamCmd = buildQueryBalanceParam(rtnDTO, txAcctNo, hbOneNo, hkSaleFlag, productType, productSubType, productCode, ip, disCode);
        QueryAcctBalanceResponse balanceResponse = null;
        if (YesOrNoEnum.NO.getCode().equals(queryWithHkInterface)) {
            balanceResponse = queryBalanceVolService.queryOldAcctBalance(queryBalanceParamCmd);
        } else {
            balanceResponse = queryBalanceVolService.queryNewAcctBalance(queryBalanceParamCmd);
        }
        if (!isSubAccount) {
            txAcctNo = checkTxAcctNo(rtnDTO, txAcctNo, hbOneNo, balanceResponse);
        }
        if (null == balanceResponse) {
            LOG.info("balanceResponse is null!");
            write(rtnDTO, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
            return;
        }
        if (super.tmsCallFail(balanceResponse)) {
            throw new BizException(balanceResponse.getReturnCode(), balanceResponse.getDescription());
        }
        // 查询是否含有私募定投
        setHighFundInvPlanFlag(rtnDTO, txAcctNo, ip, productCode);
        // 汇总信息
        rtnDTO.setTotalIncomCalStat(balanceResponse.getTotalIncomCalStat());
        rtnDTO.setTotalAsset(null == balanceResponse.getTotalMarketValue() ? 0 : balanceResponse.getTotalMarketValue().doubleValue());
        rtnDTO.setTotalUnconfirmedAmt(null == balanceResponse.getTotalUnconfirmedAmt() ? 0 : balanceResponse.getTotalUnconfirmedAmt().doubleValue());
        rtnDTO.setTotalUnconfirmedNum(balanceResponse.getTotalUnconfirmedNum());
        rtnDTO.setTotalCurrentAsset(balanceResponse.getTotalCurrentAsset());
        rtnDTO.setRedeemUnconfirmedNum(balanceResponse.getRedeemUnconfirmedNum());
        rtnDTO.setTotalCashCollection(balanceResponse.getTotalCashCollection());
        rtnDTO.setHasHKProduct(balanceResponse.getHasHKProduct());
        rtnDTO.setHasHZProduct(balanceResponse.getHasHZProduct());
        if (CollectionUtils.isEmpty(balanceResponse.getBalanceList())) {
            LOG.info("balanceResponse BalanceList is null!");
            write(rtnDTO, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
            return;
        }

        // 获取代销产品列表list
        List<String> productCodeList = getProductList(balanceResponse.getBalanceList());

        // 查询购买状态
        Map<String, BuyFundStatusBean> buyFundStatusMap = getBuyStatus(productCodeList, ip, txAcctNo);

        // 查询赎回状态
        Map<String, RedeemFundStatusBean> rdmFundStatusMap = getRdmStatus(productCodeList, ip, txAcctNo);
        Map<String, CustRepurchaseProtocolDto> custRepurchaseProtocolMap = queryCustRepurchaseProtocolMap(txAcctNo, productCodeList);

        // 查询产品信息
        Map<String, HighProductBaseInfoModel> productInfoMap = queryProductMap(productCodeList);

        // 查询产品打标信息
        Map<String, HighProductTagInfoModel> productTagInfoMap = queryProductTagInfoMap(productCodeList);

        // 查询最近预约日历
        Map<String, List<HighProductAppointmentInfoModel>> productAppointMap = queryLatestByAppintDt(productCodeList);

        // 查询产品净值状态
        Map<String, HighProductStatInfoModel> productStatMap = queryProductStatusMap(productCodeList);
        // 产品策略
        List<String> fundCodeList = getAllProductCode(balanceResponse);
        List<SimuZcpzJjInfo> infoList = smZcpzService.getSimuZcpzJjInfoList(fundCodeList);
        Map<String, SimuZcpzJjInfo> strategyMap = infoList.stream().collect(Collectors.toMap(SimuZcpzJjInfo::getJjdm, x -> x));
        Map<String, String> cpflMap = super.getCpflMap(productCodeList);

        for (BalanceBean bean : balanceResponse.getBalanceList()) {
            HoldFund fund = buildHoldFund(rtnDTO, balanceResponse, productCodeList, buyFundStatusMap, rdmFundStatusMap, custRepurchaseProtocolMap, productInfoMap, productTagInfoMap, productAppointMap, productStatMap, strategyMap, cpflMap, bean);
            rtnDTO.addFund(fund);
        }

        write(rtnDTO, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }

    /**
     * 构建持仓实体
     */
    private HoldFund buildHoldFund(IndexDTO rtnDTO, QueryAcctBalanceResponse balanceResponse, List<String> productCodeList, Map<String, BuyFundStatusBean> buyFundStatusMap, Map<String, RedeemFundStatusBean> rdmFundStatusMap, Map<String, CustRepurchaseProtocolDto> custRepurchaseProtocolMap, Map<String, HighProductBaseInfoModel> productInfoMap, Map<String, HighProductTagInfoModel> productTagInfoMap, Map<String, List<HighProductAppointmentInfoModel>> productAppointMap, Map<String, HighProductStatInfoModel> productStatMap, Map<String, SimuZcpzJjInfo> strategyMap, Map<String, String> cpflMap, BalanceBean bean) {
        HoldFund fund = new HoldFund();
        fund.setDisCode(bean.getDisCode());
        fund.setFundCode(bean.getProductCode());
        fund.setFundNameAbbr(bean.getProductName());
        fund.setProductType(bean.getProductType());
        fund.setProductSubType(bean.getProductSubType());
        fund.setNav(bean.getNav());
        fund.setNavDate(bean.getNavDt());
        fund.setNavDivFlag(bean.getNavDivFlag());
        fund.setTotalBala(bean.getBalanceVol());
        fund.setUnconfirmedAmt(bean.getUnconfirmedAmt());
        fund.setUnconfirmedVol(bean.getUnconfirmedVol());
        fund.setDayIncome(bean.getDailyAssetCurrency());
        fund.setDayIncomeRate(bean.getDayAssetRate());
        fund.setAccumIncome(bean.getAccumIncome());
        fund.setIncomeLatestDay(bean.getIncomeDt());
        fund.setFundAsset(bean.getMarketValue());
        fund.setCurrencyMarketValue(bean.getCurrencyMarketValue());
        fund.setCurrentAsset(bean.getCurrentAsset());
        fund.setCurrentAssetCurrency(bean.getCurrentAssetCurrency());
        fund.setIncomeCalStat(bean.getIncomeCalStat());
        fund.setCurrency(bean.getCurrency());
        fund.setCashCollection(bean.getCashCollection());
        if (bean.getCashCollection() != null) {
            fund.setCashCollectionStr(bean.getCashCollection().toPlainString());
        }
        fund.setCurrencyCashCollection(bean.getCurrencyCashCollection());
        fund.setNetBuyAmount(bean.getNetBuyAmount());
        fund.setCurrencyNetBuyAmount(bean.getCurrencyNetBuyAmount());
        fund.setPaidInAmt(bean.getPaidInAmt());
        fund.setPaidSubTotalRatio(bean.getPaidSubTotalRatio());
        fund.setPaidTotalAmt(bean.getPaidTotalAmt());
        fund.setFundCXQXStr(bean.getFundCXQXStr());
        fund.setScaleType(bean.getScaleType());
        fund.setHkSaleFlag(bean.getHkSaleFlag());
        fund.setStageEstablishFlag(bean.getStageEstablishFlag());
        fund.setFractionateCallFlag(bean.getFractionateCallFlag());
        fund.setStandardFixedIncomeFlag(bean.getStandardFixedIncomeFlag());
        fund.setValueDate(bean.getValueDate());
        fund.setBenchmark(bean.getBenchmark());
        fund.setDueDate(bean.getDueDate());
        fund.setRePurchaseFlag(bean.getRePurchaseFlag());
        fund.setCrisisFlag(bean.getCrisisFlag());
        fund.setYieldIncome(bean.getYieldIncome());
        fund.setYieldIncomeDt(bean.getYieldIncomeDt());
        fund.setBenchmarkType(bean.getBenchmarkType());
        fund.setEstablishDt(bean.getEstablishDt());
        fund.setSubProductCode(bean.getSubProductCode());
        fund.setStageFlag(bean.getStageFlag());
        fund.setSfhwcxg(bean.getSfhwcxg());
        // 股权产品转让标识
        fund.setOwnershipTransferIdentity(bean.getOwnershipTransferIdentity());
        // 产品分类
        fund.setDbFundType(cpflMap.get(fund.getFundCode()));

        fund.setProductSaleSource(bean.getProductSaleType());

        fund.setNaProductFeeType(bean.getNaProductFeeType());
        fund.setMarketValueExFee(bean.getMarketValueExFee());
        fund.setCurrencyMarketValueExFee(bean.getCurrencyMarketValueExFee());
        fund.setReceivManageFee(bean.getReceivManageFee());
        fund.setReceivPreformFee(bean.getReceivPreformFee());
        fund.setBalanceFactor(bean.getBalanceFactor());
        fund.setConvertFinish(bean.getConvertFinish());
        fund.setBalanceFactorDate(bean.getBalanceFactorDate());
        fund.setYieldRate(bean.getYieldRate());
        fund.setCpqxsm(bean.getCpqxsm());

        // 持仓特殊产品指标控制需求新增 20221122
        // 净值披露方式
        fund.setNavDisclosureType(bean.getNavDisclosureType());
        // 万份收益
        fund.setCopiesIncome(bean.getCopiesIncome());
        // 对于 阳光私募/非货基型券商集合 产品，若【最新净值日期】比第一笔交易的【确认日期】早3个月以上
        // 异常标志(0-否 1-是)
        fund.setAbnormalFlag(bean.getAbnormalFlag());

        // 是否控制表人为置空(0-否 1-是)，会根据特殊产品指标控制表信息，对配置字段置空 20221129 start
        fund.setFundAssetCtl(bean.getMarketValueCtl());
        fund.setCurrencyMarketValueCtl(bean.getCurrencyMarketValueCtl());
        fund.setCurrencyMarketValueExFeeCtl(bean.getCurrencyMarketValueExFeeCtl());
        fund.setMarketValueExFeeCtl(bean.getMarketValueExFeeCtl());
        fund.setCurrentAssetCtl(bean.getCurrentAssetCtl());
        fund.setCurrentAssetCurrencyCtl(bean.getCurrentAssetCurrencyCtl());
        fund.setYieldRateCtl(bean.getYieldRateCtl());
        fund.setTotalBalaCtl(bean.getBalanceVolCtl());
        fund.setUnconfirmedVolCtl(bean.getUnconfirmedVolCtl());
        fund.setNavCtl(bean.getNavCtl());
        // 是否控制表人为置空(0-否 1-是)，会根据特殊产品指标控制表信息，对配置字段置空 20221129 end

        // 千禧年持仓功能适配需求 20230216 start
        fund.setQianXiFlag(bean.getQianXiFlag());
        fund.setUnPaidInAmt(bean.getUnPaidInAmt());
        fund.setCurrencyUnPaidInAmt(bean.getCurrencyUnPaidInAmt());

        // 产品策略
        SimuZcpzJjInfo simuZcpzJjInfo = strategyMap.get(bean.getProductCode());
        if (simuZcpzJjInfo != null) {
            ProductStrategyTypeEnum productStrategy = queryAcctOtherInfoService.getProductStrategy(simuZcpzJjInfo);
            fund.setStrategy(productStrategy.getType());
            fund.setStrategyStr(productStrategy.getDesc());
        }

        //设置基金档案页地址
        processFundWebUrl(fund);

        // 开放日处理
        processOpenDate(fund, productCodeList);

        // 处理产品购买/赎回状态
        processBuyAndRdmStatus(fund, buyFundStatusMap, rdmFundStatusMap, productCodeList);

        // 处理客户复购
        processCustRepurchase(fund, custRepurchaseProtocolMap);

        // 处理预约交易日历
        processAppointDt(fund, productAppointMap, productInfoMap, productStatMap);

        // 处理打标信息
        processTagInfo(fund, productTagInfoMap);


        // 在途订单列表
        List<UnconfirmeFund> unconfirmedFunds = new ArrayList<UnconfirmeFund>();
        rtnDTO.setUnconfirmedFunds(unconfirmedFunds);
        if (CollectionUtils.isNotEmpty(balanceResponse.getUnconfirmeProducts())) {
            UnconfirmeFund unconfirmeFund = null;
            for (UnconfirmeProduct unconfirmeProduct : balanceResponse.getUnconfirmeProducts()) {
                unconfirmeFund = new UnconfirmeFund();
                BeanUtils.copyProperties(unconfirmeProduct, unconfirmeFund);
                unconfirmedFunds.add(unconfirmeFund);
            }

        }
        return fund;
    }

    private QueryBalanceParamCmd buildQueryBalanceParam(IndexDTO rtnDTO, String txAcctNo, String hboneNo, String hkSaleFlag, String productType, String productSubType, String productCode, String ip, String disCode) {
        QueryBalanceParamCmd queryBalanceParamCmd = new QueryBalanceParamCmd();
        queryBalanceParamCmd.setTxAcctNo(txAcctNo);
        queryBalanceParamCmd.setHboneNo(hboneNo);
        queryBalanceParamCmd.setProductSubType(productSubType);
        queryBalanceParamCmd.setProductType(productType);
        queryBalanceParamCmd.setProductCode(productCode);
        queryBalanceParamCmd.setHkSaleFlag(hkSaleFlag);
        queryBalanceParamCmd.setIp(ip);
        queryBalanceParamCmd.setDisCode(disCode);
        // 默认按照是否数据授权查询
        if (YesOrNoEnum.YES.getCode().equals(rtnDTO.getIsAuth())) {
            queryBalanceParamCmd.setNotFilterHkFund(YesOrNoEnum.YES.getCode());
            queryBalanceParamCmd.setNotFilterHzFund(YesOrNoEnum.YES.getCode());
            // 如果香港数据隔离,就过滤香港的产品
            if (YesOrNoEnum.YES.getCode().equals(rtnDTO.getIsHkDataQuarantine())) {
                queryBalanceParamCmd.setNotFilterHkFund(YesOrNoEnum.NO.getCode());
            }
        } else {
            queryBalanceParamCmd.setNotFilterHkFund(YesOrNoEnum.NO.getCode());
            queryBalanceParamCmd.setNotFilterHzFund(YesOrNoEnum.NO.getCode());
        }

        return queryBalanceParamCmd;
    }

    /**
     * 不分场景需要查询有没开通交易账号
     */
    private String checkTxAcctNo(IndexDTO rtnDTO, String txAcctNo, String hboneNo, QueryAcctBalanceResponse balanceResponse) {
        // 未开户且无持仓的用户需开户才能访问
        // 兼容非登录查询持仓
        if (StringUtils.isBlank(txAcctNo)) {
            txAcctNo = hboneService.queryCustNoDtoByHbone(hboneNo);
            if (StringUtils.isBlank(txAcctNo)) {
                // 设置未开户标识，有持仓时前端判断禁用按钮
                rtnDTO.setNoTxAcctNo(YesOrNoEnum.YES.getCode());
                if (balanceResponse == null || balanceResponse.getBalanceList() == null || balanceResponse.getBalanceList().isEmpty()) {
                    throw new BizException(BizErrorEnum.REQUEST_URI_BANNED.getCode(), "您还未开户，开户后方可访问此页面！");
                }
            }

        }
        return txAcctNo;
    }


    private List<String> getAllProductCode(QueryAcctBalanceResponse balanceResponse) {
        if (CollectionUtils.isNotEmpty(balanceResponse.getBalanceList())) {
            return balanceResponse.getBalanceList().stream().map(BalanceBean::getProductCode).distinct().collect(Collectors.toList());
        }
        return new ArrayList<>();
    }


    private void setHighFundInvPlanFlag(IndexDTO rtnDTO, String custNo, String ip, String productCode) {
        QueryHighFundInvPlanListResponse queryHighFundInvPlanListResponse = queryHighfundinvplan(custNo, productCode, null, ip);
        if (tmsCallSucc(queryHighFundInvPlanListResponse)) {
            List<HighFundInvPlanVo> highFundInvPlanVoList = queryHighFundInvPlanListResponse.getHighFundInvPlanVoList();
            if (!org.springframework.util.CollectionUtils.isEmpty(highFundInvPlanVoList)) {
                rtnDTO.setHighFundInvPlanFlag(YesOrNoEnum.YES.getCode());
            }
        }
    }

    private QueryHighFundInvPlanListResponse queryHighfundinvplan(String txAcctNo, String fundCode, String planId, String ip) {
        QueryHighFundInvPlanListRequest request = new QueryHighFundInvPlanListRequest();
        request.setDisCode(DisCodeEnum.HM.getCode());
        request.setOutletCode(RemoteParametersProvider.getOutletCode());
        request.setOperIp(ip);
        request.setTxChannel(RemoteParametersProvider.getTradeChannel());
        request.setDataTrack(UUID.randomUUID().toString());
        request.setTxAcctNo(txAcctNo);
        request.setFundCode(fundCode);
        request.setPlanId(planId);
        return queryHighFundInvPlanListFacade.execute(request);
    }

    /**
     * 检查臻财VIP绑定白名单（这批用户不支持自动登录，每次都要输入密码）
     *
     * @param rtnDTO
     * @param hboneNo
     * @return void
     * @author: huaqiang.liu
     * @date: 2021/5/11 10:02
     * @since JDK 1.8
     */
    private void checkIsZcvipBindWhite(IndexDTO rtnDTO, String hboneNo) {
        String isZcVipBindWhite = queryBalanceVolService.isZcVipBindWhite(hboneNo);
        if (YesOrNoEnum.YES.getCode().equals(isZcVipBindWhite)) {
            rtnDTO.setIsnologinBind("isnologinBind");
        }
    }

    private Map<String, HighProductStatInfoModel> queryProductStatusMap(List<String> productCodeList) {
        List<HighProductStatInfoModel> highProductStatInfoModelList = highProductService.getProductStatInfo(productCodeList, null);
        Map<String, HighProductStatInfoModel> productStatMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(highProductStatInfoModelList)) {
            for (HighProductStatInfoModel highProductStatInfoModel : highProductStatInfoModelList) {
                productStatMap.put(highProductStatInfoModel.getFundCode(), highProductStatInfoModel);
            }
        }

        return productStatMap;
    }


    /**
     * 开放日处理
     *
     * @param fund
     * @param productCodeList
     */
    private void processOpenDate(HoldFund fund, List<String> productCodeList) {
        // CRM直销产品, 不需要处理
        if (!productCodeList.contains(fund.getFundCode())) {
            return;
        }

        //查询产品购买开放日
        HighProductAppointmentInfoModel productAppointmentInfoModel = highProductService.getAppointmentInfo(fund.getFundCode(), "0",
                ShareClassEnum.A.getCode(), Constants.DIS_CODE_HOWBUY, new Date());
        if (null != productAppointmentInfoModel) {
            fund.setOpenStartDate(productAppointmentInfoModel.getOpenStartDt());
            fund.setOpenEndDate(productAppointmentInfoModel.getOpenEndDt());
        }
    }

    /**
     * 处理产品购买/赎回状态
     */
    private void processBuyAndRdmStatus(HoldFund fund, Map<String, BuyFundStatusBean> buyFundStatusMap,
                                        Map<String, RedeemFundStatusBean> rdmFundStatusMap, List<String> productCodeList) {
        // 产品购买状态
        fund.setCanBuy("0");
        // 产品赎回状态
        fund.setCanRedeem("0");
        if (!productCodeList.contains(fund.getFundCode())) {
            return;
        }

        BuyFundStatusBean buyFundStatusBean = buyFundStatusMap.get(fund.getFundCode());
        if (buyFundStatusBean != null) {
            fund.setCanBuy(buyFundStatusBean.getBuyStatus());
            fund.setInDirectBlackList("0");
            fund.setFundBuyStatus(buyFundStatusBean.getFundBuyStatus());
            if ("0".equals(buyFundStatusBean.getBuyStatus()) && "5".equals(buyFundStatusBean.getBuyStatusType())) {
                fund.setInDirectBlackList("1");
            }
        }

        RedeemFundStatusBean redeemFundStatusBean = rdmFundStatusMap.get(fund.getFundCode());
        if (redeemFundStatusBean != null) {
            fund.setCanRedeem(redeemFundStatusBean.getRedeemStatus());
            // 可赎回份额
            fund.setAvailVol(redeemFundStatusBean.getAvailVol());
            // 锁定份额
            fund.setLockVol(redeemFundStatusBean.getLockVol());
        }
    }


    /**
     * 查询代销系统产品的赎回状态
     *
     * @param productCodeList
     * @param custIP
     * @param custNo
     * @return
     */
    private Map<String, RedeemFundStatusBean> getRdmStatus(List<String> productCodeList, String custIP, String custNo) {
        QueryRedeemFundStatusRequest request = new QueryRedeemFundStatusRequest();
        request.setProductCodeList(productCodeList);
        // 获取好买分销代码列表
        List<String> disCodeList = queryBalanceVolService.getDisCodeList();
        request.setDisCodeList(disCodeList);
        request.setOutletCode(Constants.OUTLET_CODE_HOWBUY);
        request.setOperIp(custIP);
        request.setTxAcctNo(custNo);
        request.setTxChannel(RemoteParametersProvider.getTradeChannel());
        QueryRedeemFundStatusResponse response = queryRedeemFundStatusFacade.execute(request);
        Map<String, RedeemFundStatusBean> redeemFundStatusMap = new HashMap<String, RedeemFundStatusBean>();
        if (response != null && CollectionUtils.isNotEmpty(response.getRedeemFundStatusList())) {
            for (RedeemFundStatusBean bean : response.getRedeemFundStatusList()) {
                redeemFundStatusMap.put(bean.getProductCode(), bean);
            }
        }

        return redeemFundStatusMap;
    }

    /**
     * 获取代销系统产品购买状态
     *
     * @param productCodeList
     * @param custIP
     * @param custNo
     * @return
     */
    private Map<String, BuyFundStatusBean> getBuyStatus(List<String> productCodeList, String custIP, String custNo) {
        QueryBuyFundStatusRequest request = new QueryBuyFundStatusRequest();
        request.setProductCodeList(productCodeList);
        request.setDisCode(Constants.DIS_CODE_HOWBUY);
        request.setOutletCode(Constants.OUTLET_CODE_HOWBUY);
        request.setOperIp(custIP);
        request.setTxAcctNo(custNo);
        request.setTxChannel(RemoteParametersProvider.getTradeChannel());
        QueryBuyFundStatusResponse response = queryBuyFundStatusFacade.execute(request);

        Map<String, BuyFundStatusBean> buyFundStatusMap = new HashMap<String, BuyFundStatusBean>();
        if (response != null && CollectionUtils.isNotEmpty(response.getBuyFundStatusList())) {
            for (BuyFundStatusBean bean : response.getBuyFundStatusList()) {
                buyFundStatusMap.put(bean.getProductCode(), bean);
            }
        }

        return buyFundStatusMap;
    }


    /**
     * 客户资产证明/Kyc信息处理
     *
     * @param hboneNo
     * @param rtnDTO
     */
    private void processAssetCertificateAndKyc(String hboneNo, IndexDTO rtnDTO) {
        // 资产证明
        QueryCurrentAssetCertificateStatusRequest request = new QueryCurrentAssetCertificateStatusRequest();
        request.setHboneNo(hboneNo);
        QueryCurrentAssetCertificateStatusResponse response = queryCurrentAssetCertificateStatusService.execute(request);

        String isVerifyAssetCertify = "1"; //是否需要资产验证  1是 0否
        rtnDTO.setIsVerifyAssetCertify(isVerifyAssetCertify);
        rtnDTO.setVerifyStatus(null != response ? response.getLatestCertificateStatus() : null);


        // KYC信息
        KycModel kycModel = kycService.queryKycInfo(hboneNo);
        if (null != kycModel && null != kycModel.getInvestorType() && "PRO".equals(kycModel.getInvestorType())) {
            rtnDTO.setIsProfessor("1");
        }
    }

    /**
     * @api {post}  /simu/user/simurecommend.htm 个人持仓的产品推荐
     * @apiGroup index
     * @APIName /simu/user/simurecommend.htm
     * @apiDescription 当个人持仓为空，PC页面需要推荐给用户产品信息
     * @apiSuccess {String} desc 返回描述
     * @apiSuccess {String} code 返回码
     * @apiSuccess {Object} body 数据
     * @apiSuccessExample {json} Response Example { "body": {
     * <p>
     * }, "desc": "成功", "code": "0000" }
     */
    @RequestMapping("/simu/user/simurecommend.htm")
    public void simurecommend(HttpServletRequest request, HttpServletResponse response) throws Exception {
        //List<String> hmtjKey = new LinkedList<String>();//好买推荐Key
        Map<String, List<SimuRecommendRecord>> hmtjMap = new HashMap<String, List<SimuRecommendRecord>>(); //好买推荐

        List<CmsRecommColumn> smtjList = cmsRecommendService.getCmsRecommColumnsByParamer("wzsmsyhmtj");
        if (null != smtjList && smtjList.size() > 0) {
            if (null != smtjList.get(0).getCmsRecommColumnList() && smtjList.get(0).getCmsRecommColumnList().size() > 0) {
                for (CmsRecommColumn crc : smtjList.get(0).getCmsRecommColumnList()) {
                    List<CmsRecommProduct> yjTjList = crc.getCmsRecommProductList();
                    if (null != yjTjList && !yjTjList.isEmpty()) {
                        //hmtjKey.add(crc.getName());
                        hmtjMap.put(crc.getName(), smFundProfileService.getSmRecommendRecord(yjTjList, "PC"));
                    }
                }
            }
        }
        write(hmtjMap, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }

    /**
     * processFundWebUrl:(处理基金档案页url)
     *
     * @param fund
     * <AUTHOR>
     * @date 2017年11月21日 下午1:19:46
     */
    private void processFundWebUrl(HoldFund fund) {
        SmxxDto smxxDto = null;
        try {
            smxxDto = smxxService.getSmxxByCodeAll(fund.getFundCode());
        } catch (Exception e) {
            log.error("smxxService getSmxxByCodeAll error fundcode:{}", fund.getFundCode(), e);
        }

        if (null != smxxDto) {
            fund.setCptjly(smxxDto.getCptjly());
            fund.setEjflName(smxxDto.getEjfl().getView());
        }
        fund.setWebUrl(getWebUrl(smxxDto, fund.getFundCode(), fund.getDbFundType()));// 设置档案页
    }


    /**
     * 获取代销产品列表list
     *
     * @param balanceList
     * @return
     */
    private List<String> getProductList(List<BalanceBean> balanceList) {
        BalanceBean balanceBean = null;
        List<String> list = new ArrayList<String>();
        for (int i = 0; i < balanceList.size(); i++) {
            balanceBean = balanceList.get(i);
            if (ScaleTypeEnum.DIRECT.getCode().equals(balanceBean.getScaleType())) {
                continue;
            }
            if (list.contains(balanceBean.getProductCode())) {
                continue;
            }
            list.add(balanceBean.getProductCode());
        }
        return list;
    }

    private Map<String, CustRepurchaseProtocolDto> queryCustRepurchaseProtocolMap(String txAcctNo, List<String> fundCodes) {
        Map<String, CustRepurchaseProtocolDto> custRepurchaseProtocolMap = new HashMap<>();
        try {
            QueryCustRepurchaseProtocolRequest request = new QueryCustRepurchaseProtocolRequest();
            request.setTxAcctNo(txAcctNo);
            request.setFundCodes(fundCodes);
            QueryCustRepurchaseProtocolResposne resp = queryCustRepurchaseProtocolFacade.execute(request);
            if (CollectionUtils.isNotEmpty(resp.getCustRepurchaseProtocolList())) {
                CustRepurchaseProtocolDto custRepurchaseProtocolDto = null;
                for (CustRepurchaseProtocolBean custRepurchaseProtocolBean : resp.getCustRepurchaseProtocolList()) {
                    custRepurchaseProtocolDto = new CustRepurchaseProtocolDto();
                    BeanUtils.copyProperties(custRepurchaseProtocolBean, custRepurchaseProtocolDto);
                    custRepurchaseProtocolMap.put(custRepurchaseProtocolBean.getFundCode(), custRepurchaseProtocolDto);
                }
            }
        } catch (Exception e) {
            log.error("IndexSimuController|queryCustRepurchaseProtocol|error:{}", e);
        }

        return custRepurchaseProtocolMap;
    }

    private void processCustRepurchase(HoldFund fund, Map<String, CustRepurchaseProtocolDto> custRepurchaseProtocolMap) {
        if (custRepurchaseProtocolMap == null || custRepurchaseProtocolMap.size() == 0) {
            return;
        }
        CustRepurchaseProtocolDto custRepurchaseProtocolDto = custRepurchaseProtocolMap.get(fund.getFundCode());
        if (custRepurchaseProtocolDto != null) {
            fund.setCanModifyRepurchaseProtocolFlag(custRepurchaseProtocolDto.getCanModify());
            fund.setCustRepurchaseProtocol(custRepurchaseProtocolDto);
            fund.setProductRepurchaseFlag(custRepurchaseProtocolDto.getProductRepurchaseFlag());
        }
    }

    private void processBuyAppintDay(HoldFund fund, Map<String, List<HighProductAppointmentInfoModel>> productAppointMap,
                                     Map<String, HighProductBaseInfoModel> productInfoMap, Map<String, HighProductStatInfoModel> productStatMap,
                                     WorkDayModel workDayModel) {
        HighProductBaseInfoModel highProductBaseInfoModel = productInfoMap.get(fund.getFundCode());
        if (workDayModel != null && highProductBaseInfoModel != null && highProductBaseInfoModel.getIpoEndDt() != null
                && workDayModel.getWorkday().compareTo(highProductBaseInfoModel.getIpoEndDt()) <= 0) {
            // 认购
            List<HighProductAppointmentInfoModel> appointList = productAppointMap.get(fund.getFundCode());
            for (HighProductAppointmentInfoModel highProductAppointmentInfoModel : appointList) {
                if (BusinessCodeEnum.SUBS.getMCode().equals(highProductAppointmentInfoModel.getmBusiCode())) {
                    fund.setBuyAppointStartDt(highProductAppointmentInfoModel.getAppointStartDt());
                    fund.setBuyAppointEndDt(highProductAppointmentInfoModel.getApponitEndDt());
                    fund.setPayEndDate(highProductAppointmentInfoModel.getPayDeadlineDtm());
                    break;
                }
            }
        } else if (highProductBaseInfoModel != null && BusiUtil.isSupportAdvanceBuy(highProductBaseInfoModel.getIsScheduledTrade())) {
            List<HighProductAppointmentInfoModel> appointList = productAppointMap.get(fund.getFundCode());
            if (appointList != null) {
                for (HighProductAppointmentInfoModel highProductAppointmentInfoModel : appointList) {
                    if (BusinessCodeEnum.PURCHASE.getMCode().equals(highProductAppointmentInfoModel.getmBusiCode())
                            || BusinessCodeEnum.SUBS.getMCode().equals(highProductAppointmentInfoModel.getmBusiCode())) {
                        fund.setBuyAppointStartDt(highProductAppointmentInfoModel.getAppointStartDt());
                        fund.setBuyAppointEndDt(highProductAppointmentInfoModel.getApponitEndDt());
                        fund.setPayEndDate(highProductAppointmentInfoModel.getPayDeadlineDtm());

                        break;
                    }
                }
            }
        } else {
            HighProductStatInfoModel highProductStatInfoModel = productStatMap.get(fund.getFundCode());
            if (workDayModel != null && highProductStatInfoModel != null && (FundNavStatusType.isPur(highProductStatInfoModel.getFundStat())
                    || FundNavStatusType.isSubs(highProductStatInfoModel.getFundStat()))) {
                fund.setBuyAppointStartDt(workDayModel.getWorkday());
                fund.setBuyAppointEndDt(workDayModel.getWorkday());
                fund.setPayEndDate(workDayModel.getWorkday() + DEFAULT_TIME);
            }
        }
    }

    private void processSellAppintDay(HoldFund fund, Map<String, List<HighProductAppointmentInfoModel>> productAppointMap,
                                      Map<String, HighProductBaseInfoModel> productInfoMap, Map<String, HighProductStatInfoModel> productStatMap,
                                      WorkDayModel workDayModel) {
        HighProductBaseInfoModel highProductBaseInfoModel = productInfoMap.get(fund.getFundCode());
        if (highProductBaseInfoModel != null && BusiUtil.isSupportAdvanceRedeem(highProductBaseInfoModel.getIsScheduledTrade())) {
            List<HighProductAppointmentInfoModel> appointList = productAppointMap.get(fund.getFundCode());
            if (appointList != null) {
                for (HighProductAppointmentInfoModel highProductAppointmentInfoModel : appointList) {
                    if (BusinessCodeEnum.REDEEM.getMCode().equals(highProductAppointmentInfoModel.getmBusiCode())) {
                        fund.setSellAppointStartDt(highProductAppointmentInfoModel.getAppointStartDt());
                        fund.setSellAppointEndDt(highProductAppointmentInfoModel.getApponitEndDt());

                        break;
                    }
                }
            }
        } else {
            HighProductStatInfoModel highProductStatInfoModel = productStatMap.get(fund.getFundCode());
            if (workDayModel != null && highProductStatInfoModel != null && (FundNavStatusType.isRedeem(highProductStatInfoModel.getFundStat()))) {
                fund.setSellAppointStartDt(workDayModel.getWorkday());
                fund.setSellAppointEndDt(workDayModel.getWorkday());
            }
        }


    }

    private void processAppointDt(HoldFund fund, Map<String, List<HighProductAppointmentInfoModel>> productAppointMap,
                                  Map<String, HighProductBaseInfoModel> productInfoMap, Map<String, HighProductStatInfoModel> productStatMap) {
        log.info("processAppointDt:fund:{}, productAppointMap:{}, productInfoMap:{}", JSON.toJSONString(fund), JSON.toJSONString(productAppointMap), JSON.toJSONString(productInfoMap));
        try {
            WorkDayModel workDayModel = tradeDayService.getWorkDayModel(new Date());
            processBuyAppintDay(fund, productAppointMap, productInfoMap, productStatMap, workDayModel);
            processSellAppintDay(fund, productAppointMap, productInfoMap, productStatMap, workDayModel);
        } catch (Exception e) {
            log.error("processAppointDt error:{}", e.getMessage(), e);
        }

    }

    private void processTagInfo(HoldFund fund, Map<String, HighProductTagInfoModel> tagInfoMap) {
        HighProductTagInfoModel model = tagInfoMap.get(fund.getFundCode());
        if (model != null && StringUtils.isNotBlank(model.getGaoYiLingShanFlag())) {
            fund.setGaoYiLingShanFlag(model.getGaoYiLingShanFlag());
        } else {
            fund.setGaoYiLingShanFlag(YesOrNoEnum.NO.getCode());
        }
    }

    private Map<String, List<HighProductAppointmentInfoModel>> queryLatestByAppintDt(List<String> fundCodes) {
        if (CollectionUtils.isEmpty(fundCodes)) {
            return new HashMap<>();
        }

        String appDtm = DateUtils.formatToString(new Date(), DateUtils.YYYYMMDDHHMMSS);
        List<HighProductAppointmentInfoModel> productAppointList = highProductService.getLatestByAppintDt(fundCodes, appDtm);

        Map<String, List<HighProductAppointmentInfoModel>> productAppointMap = new HashMap<>(fundCodes.size());
        for (HighProductAppointmentInfoModel highProductAppointmentInfoModel : productAppointList) {
            List<HighProductAppointmentInfoModel> productAppointDtList = productAppointMap.get(highProductAppointmentInfoModel.getProductId());
            if (productAppointDtList == null) {
                productAppointDtList = new ArrayList<>();
            }

            productAppointDtList.add(highProductAppointmentInfoModel);
            productAppointMap.put(highProductAppointmentInfoModel.getProductId(), productAppointDtList);
        }

        return productAppointMap;
    }

    private Map<String, HighProductBaseInfoModel> queryProductMap(List<String> fundCodes) {
        List<HighProductBaseInfoModel> productInfoList = highProductService.getHighProductBaseInfo(fundCodes);

        Map<String, HighProductBaseInfoModel> productInfoMap = new HashMap<>();
        if (productInfoList != null) {
            for (HighProductBaseInfoModel highProductBaseInfoModel : productInfoList) {
                productInfoMap.put(highProductBaseInfoModel.getFundCode(), highProductBaseInfoModel);
            }
        }

        return productInfoMap;
    }

    private Map<String, HighProductTagInfoModel> queryProductTagInfoMap(List<String> fundCodes) {
        List<HighProductTagInfoModel> productTagInfoList = highProductService.getTagInfoByFundCodeList(fundCodes);

        Map<String, HighProductTagInfoModel> productTagInfoMap = new HashMap<>();
        if (productTagInfoList != null) {
            for (HighProductTagInfoModel model : productTagInfoList) {
                productTagInfoMap.put(model.getFundCode(), model);
            }
        }

        return productTagInfoMap;
    }

    /**
     * @api {post}  /simu/user/finReceipt.htm 查询在途笔数、资金到账提醒数据
     * @apiGroup SIMU-CGI
     * @APIName /simu/user/finReceipt.htm
     * @apiDescription 查询在途笔数、资金到账提醒数据
     * @apiParam {String} [subAccountId] 子关联账户id，查询关联账户资产用
     * @apiSuccess {String} desc 返回描述
     * @apiSuccess {String} code 返回码
     * @apiSuccess {Object} body 数据
     * @apiSuccess (body) {int} buyUnrefundedPiece 购买待退款订单数
     * @apiSuccess (body) {int} redeemUnrefundedPiece 赎回待回款订单数
     * @apiSuccess (body) {String} hasUnAuthProduct 是否有未授权产品  0:没有,1:有
     * @apiSuccess (body) {Array} unpaidList<String> 待付款订单（list值为订单号）
     * @apiSuccess (unpaidList) {String} string 待付款订单号
     * @apiSuccess (unpaidList) {String} string 订单类型：0-直销、1-代销
     * @apiSuccess (body) {Array} unconfirmedList<String> 待确认订单（list值为订单号）
     * @apiSuccess (unconfirmedList) {String} string 待确认订单号
     * @apiSuccess (unconfirmedList) {String} string 订单类型：0-直销、1-代销
     * @apiSuccessExample {json} Response Example { "body": {
     * <p>
     * }, "desc": "成功", "code": "0000" }
     */
    @RequestMapping("/simu/user/finReceipt.htm")
    public void finReceipt(HttpServletRequest request, HttpServletResponse response) throws Exception {
        QueryFinReceiptParamCmd queryFinReceiptParamCmd = buildParamCmd(request);
        log.info("finReceipt-查询在途信息,queryFinReceiptParamCmd={}", JSON.toJSONString(queryFinReceiptParamCmd));
        QueryFinReceiptResponse finReceiptResponse = queryBalanceVolService.queryFinReceipt(queryFinReceiptParamCmd);
        FinReceiptDTO rtnDTO = new FinReceiptDTO();
        rtnDTO.setIsAuth(queryFinReceiptParamCmd.getIsDataAuth());
        if (finReceiptResponse == null) {
            log.info("finReceiptResponse is null,queryFinReceiptParamCmd:{}", JSON.toJSONString(queryFinReceiptParamCmd));
        } else {
            // 购买待退款订单数
            rtnDTO.setBuyUnrefundedPiece(finReceiptResponse.getBuyUnrefundedPiece());
            // 赎回待回款订单数
            rtnDTO.setRedeemUnrefundedPiece(finReceiptResponse.getRedeemUnrefundedPiece());
            // 待付款订单 20221229
            rtnDTO.setUnpaidList(finReceiptResponse.getUnpaidList());
            // 待确认订单 20221229
            rtnDTO.setUnconfirmedList(finReceiptResponse.getUnconfirmedList());
        }

        write(rtnDTO, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }

    /**
     * 构建查询在途入参
     */
    private QueryFinReceiptParamCmd buildParamCmd(HttpServletRequest request) {
        TradeSession loginInfo = this.getCustSession();
        if (null == loginInfo || null == loginInfo.getUser()) {
            LOG.info("buildParamCmd-loginInfo is null!");
            throw new BizException(BizErrorEnum.NEED_RELOGIN.getCode(), BizErrorEnum.NEED_RELOGIN.getDesc());
        }
        // 查询高端用户持仓信息
        String ip = WebUtil.getCustIP(request);
        // 子关联账户id
        String subAccountId = getString("subAccountId");
        String hboneNo = loginInfo.getUser().getHboneNo();
        String txAcctNo = null;
        if (StringUtils.isBlank(subAccountId)) {
            // 查询个人首页
            txAcctNo = loginInfo.getUser().getTxAcctNo();
        } else {
            // 查询关联账户持仓
            // 查询当前账户的关联账户
            String subHboneNo = getSubHbOneNo(hboneNo, subAccountId);
            // 查询关联账户客户信息
            MemberCustInfoModel custInfo = hboneService.queryCustInfoByHboneNo(subHboneNo);
            if (custInfo == null) {
                log.error("查询关联账户客户信息失败");
                throw new BizException(BizErrorEnum.SYSTEM_ERROR.getCode(), BizErrorEnum.SYSTEM_ERROR.getDesc());
            }
            txAcctNo = custInfo.getCustNo();
            hboneNo = subHboneNo;
        }

        // 查询资金到账提醒数据 ********
        // 查询是否授权
        AcctDataAuthDto acctDataAuthInfo = accCenterService.getAcctDataAuthInfo(hboneNo);
        QueryFinReceiptParamCmd queryFinReceiptParamCmd = new QueryFinReceiptParamCmd();
        if (YesOrNoEnum.YES.getCode().equals(acctDataAuthInfo.getIsDataAuth())) {
            queryFinReceiptParamCmd.setNotFilterHkFund(YesOrNoEnum.YES.getCode());
            queryFinReceiptParamCmd.setNotFilterHzFund(YesOrNoEnum.YES.getCode());
            if (YesOrNoEnum.YES.getCode().equals(acctDataAuthInfo.getIsHkDataQuarantine())) {
                queryFinReceiptParamCmd.setNotFilterHkFund(YesOrNoEnum.NO.getCode());
            }
        } else {
            queryFinReceiptParamCmd.setNotFilterHkFund(YesOrNoEnum.NO.getCode());
            queryFinReceiptParamCmd.setNotFilterHzFund(YesOrNoEnum.NO.getCode());
        }
        queryFinReceiptParamCmd.setHbOneNo(hboneNo);
        queryFinReceiptParamCmd.setTxAcctNo(txAcctNo);
        queryFinReceiptParamCmd.setIp(ip);
        queryFinReceiptParamCmd.setIsDataAuth(acctDataAuthInfo.getIsDataAuth());
        return queryFinReceiptParamCmd;
    }
}
