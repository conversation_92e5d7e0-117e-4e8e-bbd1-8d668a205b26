/**
 * Copyright (c) 2021, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cgi.trade.simu.controller.trade.relatedaccount;

import com.alibaba.fastjson.JSON;
import com.howbuy.acccenter.facade.query.queryrelatedaccount.bean.RelatedAccountBean;
import com.howbuy.acccenter.facade.query.queryrelatedaccount.bean.RelatedAccountInviteBean;
import com.howbuy.acccenter.facade.query.queryrelatedaccount.bean.SubRelatedAccountBean;
import com.howbuy.acccenter.facade.trade.relatedaccount.GenerateInviteCodeResponse;
import com.howbuy.cgi.common.CGIConstants;
import com.howbuy.cgi.common.enums.BizErrorEnum;
import com.howbuy.cgi.common.exception.BizException;
import com.howbuy.cgi.common.util.RemoteUtil;
import com.howbuy.cgi.common.validate.ValidateUtil;
import com.howbuy.cgi.trade.ccms.simu.SimuCcmsServiceRegister;
import com.howbuy.cgi.trade.simu.common.enums.FamilyRoleEnum;
import com.howbuy.cgi.trade.simu.common.enums.InviteStatsEnum;
import com.howbuy.cgi.trade.simu.controller.AbstractSimuCGIController;
import com.howbuy.cgi.trade.simu.model.cmd.QueryInviteDtlCmd;
import com.howbuy.cgi.trade.simu.model.cmd.SendInviteCodeCmd;
import com.howbuy.cgi.trade.simu.model.cmd.UseInviteCodeCmd;
import com.howbuy.cgi.trade.simu.model.dto.relatedaccount.CanInviteDto;
import com.howbuy.cgi.trade.simu.model.dto.relatedaccount.CanInviteDto.*;
import com.howbuy.cgi.trade.simu.model.dto.relatedaccount.InviteCodeDto;
import com.howbuy.cgi.trade.simu.model.dto.relatedaccount.InviteDtlDto;
import com.howbuy.cgi.trade.simu.model.dto.relatedaccount.SendInviteCodeDto;
import com.howbuy.cgi.trade.simu.model.relatedaccount.QueryInvitesModel;
import com.howbuy.cgi.trade.simu.service.relatedaccount.AttackJudgerService;
import com.howbuy.cgi.trade.simu.service.relatedaccount.InviteService;
import com.howbuy.cgi.trade.simu.service.relatedaccount.MessageService;
import com.howbuy.cgi.trade.simu.service.relatedaccount.RelatedAccountService;
import com.howbuy.cgi.trade.simu.service.task.HowBuyRunTaskUil;
import com.howbuy.cgi.trade.simu.service.task.SendAddFamilyMemberMsgTask;
import com.howbuy.cgi.trade.simu.util.RelatedAccoutUtils;
import com.howbuy.crm.nt.conscust.dto.ConscustInfoDomain;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.trade.account.model.accplaintext.CustMobileModel;
import com.howbuy.trade.account.model.hbone.MemberCustInfoModel;
import com.howbuy.trade.account.service.account.AccPlaintextService;
import com.howbuy.trade.account.service.hbone.HboneService;
import com.howbuy.trade.common.session.model.TradeSession;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 请在此添加描述
 * <AUTHOR>
 * @date 2021/7/9 15:48
 * @since JDK 1.8
 */
@Controller
public class InviteController extends AbstractSimuCGIController {

    private static final Logger LOG = LogManager.getLogger(InviteController.class);
    /** 家庭成员总数 */
    private static final int TOTAL_FAMILY_MEMBER = 10;
    /** 子女总数 */
    private static final int TOTAL_CHILDREN = 5;
    /** 不可被邀请 */
    private static final String ERROR_CAN_NOT_INVITED = "您输入的手机号不可被邀请，请重新输入。";
    /** 重复邀请 */
    private static final String ERROR_REINVITE = "重复邀请，请重新输入。";
    /** 信息不全 */
    private static final String ERROR_LOSE_INFO = "被邀请人身份信息不全。";
    /** 年龄不符 */
    private static final String ERROR_AGE_NOT_MATCH = "被邀请人年龄与所选角色不符。";
    /** 性别不符 */
    private static final String ERROR_SEX_NOT_MATCH = "被邀请人性别与所选角色不符。";
    /** 已存在关联账户 */
    private static final String ERROR_EXISTS = "被邀请人已存在关联账户。";
    /** 非同一投顾 */
    private static final String ERROR_NOT_SAME_CONS = "被邀请人与您非同一投顾。";

    @Autowired
    private SimuCcmsServiceRegister simuCcmsServiceRegister;
    @Autowired
    private RelatedAccountService relatedAccountService;
    @Autowired
    private InviteService inviteService;
    @Autowired
    private AccPlaintextService accPlaintextService;
    @Autowired
    private HboneService hboneService;
    @Autowired
    private MessageService messageService;
    @Autowired
    private AttackJudgerService attackJudgerService;
    @Autowired
    private HowBuyRunTaskUil howBuyRunTaskUil;

    /**
     * @api {POST} /simu/relatedaccount/querycaninvitelist.htm 查询可邀请列表
     * @apiVersion 1.0.0
     * @apiGroup RELATED-ACCOUNT
     * @apiName queryCanInviteList
     * @apiDescription 查询可邀请列表
     * @apiParam {String} accountId 家庭关联账户id
     * @apiSuccess (响应结果) {Array} roleList 角色列表
     * @apiSuccess (响应结果) {String} roleList.role 角色 0-本人;1-父亲;2-母亲;3-爱人;4-爱人父;5-爱人母;6-儿子;7-女儿
     * @apiSuccess (响应结果) {String} roleList.inviteStats 1-可邀请;2-邀请中;3-已邀请
     * @apiSuccessExample 响应结果示例
     * {"roleList":[{"inviteStats":"NCNBISJ6e","role":"R3fnq0RL"}]}
     */
    @RequestMapping("/simu/relatedaccount/querycaninvitelist.htm")
    public void queryCanInviteList(HttpServletRequest request, HttpServletResponse response) throws Exception {
        TradeSession loginInfo = this.getCustSession();
        if (null == loginInfo || null == loginInfo.getUser()) {
            LOG.info("loginInfo is null!");
            throw new BizException(BizErrorEnum.NEED_RELOGIN.getCode(), BizErrorEnum.NEED_RELOGIN.getDesc());
        }

        CanInviteDto resDto = new CanInviteDto();
        // 关联账户id
        String accountId = getString("accountId");
        if (StringUtils.isBlank(accountId)) {
            throw new BizException(BizErrorEnum.REQUEST_PARAMS_ERROR.getCode(), BizErrorEnum.REQUEST_PARAMS_ERROR.getDesc());
        }

        // 查询家庭成员
        RelatedAccountBean account = relatedAccountService.queryFamilyRelatedAccount(null, accountId);
        if (account == null) {
            LOG.error("家庭不存在,accountId:{}", accountId);
            throw new BizException(BizErrorEnum.SYSTEM_ERROR.getCode(), "家庭不存在");
        }
        if (!loginInfo.getUser().getHboneNo().equals(account.getHboneNo())) {
            LOG.error("当前客户不是家庭主。current:{} master:{}", loginInfo.getUser().getHboneNo(), account.getHboneNo());
            throw new BizException(BizErrorEnum.SYSTEM_ERROR.getCode(), BizErrorEnum.SYSTEM_ERROR.getDesc());
        }
        List<SubRelatedAccountBean> invitedList = account.getSubRelatedAccountList();
        if (invitedList == null) {
            invitedList = Collections.emptyList();
        }

        // 查询邀请中成员
        QueryInvitesModel model = new QueryInvitesModel();
        model.setAccountId(accountId);
        model.setUsed(false);
        List<RelatedAccountInviteBean> invitingList = inviteService.queryInvitesEffective(model);
        if (invitingList == null) {
            invitingList = Collections.emptyList();
        }

        // 生成邀请列表
        List<Role> roleList = genRoleList(invitedList, invitingList);

        // 排序
        Collections.sort(roleList);

        resDto.setRoleList(roleList);
        write(resDto, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }

    /**
     * 生成邀请角色列表
     * @param invitedList
     * @param invitingList
     * @return java.util.List<com.howbuy.cgi.trade.simu.model.dto.relatedaccount.CanInviteDto.Role>
     * @author: huaqiang.liu
     * @date: 2021/7/15 17:04
     * @since JDK 1.8
     */
    private List<Role> genRoleList(List<SubRelatedAccountBean> invitedList, List<RelatedAccountInviteBean> invitingList) {
        Map<String, Role> familyRoleMap = createInviteRoleMap();
        int childrenNum = 0;
        // 已邀请
        for (SubRelatedAccountBean bean : invitedList) {
            String roleValue = bean.getRelatedAccountRole().getValue();
            if (FamilyRoleEnum.SON.getCode().equals(roleValue) || FamilyRoleEnum.DAUGHTER.getCode().equals(roleValue)) {
                // 子女有多个，这里仅统计数量
                childrenNum++;
            } else {
                // 修改为已邀请
                familyRoleMap.get(roleValue).setInviteStats(InviteStatsEnum.INVITED.getCode());
            }
        }
        // 邀请中
        for (RelatedAccountInviteBean bean : invitingList) {
            String roleValue = bean.getRelatedAccountRole().getValue();
            if (FamilyRoleEnum.SON.getCode().equals(roleValue) || FamilyRoleEnum.DAUGHTER.getCode().equals(roleValue)) {
                // 子女有多个，这里仅统计数量
                childrenNum++;
            } else {
                // 修改为邀请中
                familyRoleMap.get(roleValue).setInviteStats(InviteStatsEnum.INVITING.getCode());
            }
        }

        // 检查人数（子女限制为5个，总数不可能超过10个，不校验总数）
        if (TOTAL_CHILDREN <= childrenNum) {
            // 子女达到上限，不可邀请子女
            familyRoleMap.get(FamilyRoleEnum.SON.getCode()).setInviteStats(InviteStatsEnum.FULL.getCode());
            familyRoleMap.get(FamilyRoleEnum.DAUGHTER.getCode()).setInviteStats(InviteStatsEnum.FULL.getCode());
        }

        return new ArrayList<>(familyRoleMap.values());
    }

    /**
     * 创建邀请角色map
     * @param
     * @return java.util.Map<java.lang.String,com.howbuy.cgi.trade.simu.model.dto.relatedaccount.CanInviteDto.Role>
     * @author: huaqiang.liu
     * @date: 2021/7/22 15:20
     * @since JDK 1.8
     */
    private Map<String, Role> createInviteRoleMap() {
        Map<String, Role> map = new HashMap<>(FamilyRoleEnum.values().length);
        for (FamilyRoleEnum e : FamilyRoleEnum.values()) {
            Role role = new Role();
            role.setRole(e.getCode());
            role.setInviteStats(InviteStatsEnum.CAN_INVITE.getCode());
            map.put(e.getCode(), role);
        }
        return map;
    }

    /**
     * @api {post}  /simu/relatedaccount/sendinvitecode.htm 发送邀请码
     * @apiGroup RELATED-ACCOUNT
     * @APIName /simu/relatedaccount/sendinvitecode.htm
     * @apiDescription 发送邀请码
     *
     * @apiParam {String} accountId 家庭关联账户id
     * @apiParam {String} role 角色 1-父亲;2-母亲;3-爱人;4-爱人父;5-爱人母;6-儿子;7-女儿
     * @apiParam {String} mobile 被邀请人手机
     * @apiParam {String} resendFlag 重发标识 0否1是
     *
     * @apiSuccess {String} desc 返回描述
     * @apiSuccess {String} code 返回码
     *                              H007-发送失败（desc为提醒内容）
     *                              H008-失败次数过多（desc为重试冷静期）
     *                              B451-操作频繁，请稍后再试
     * @apiSuccess {Object} body 数据
     * @apiSuccess (body) {String} inviteId 邀请id
     * @apiSuccess (body) {String} timeLimit 到期时间yyyyMMddHHmmss
     *
     */
    @RequestMapping("/simu/relatedaccount/sendinvitecode.htm")
    public void sendInviteCode(HttpServletRequest request, HttpServletResponse response) throws Exception {
        SendInviteCodeCmd cmd = getCommand(SendInviteCodeCmd.class);
        ValidateUtil.assertValid(cmd);
        TradeSession loginInfo = getCustSession();
        String hboneNo = loginInfo.getUser().getHboneNo();

        // 校验是否锁定
        attackJudgerService.checkSendIsLock(hboneNo);

        try {
            // 校验并发送邀请链接
            SendInviteCodeDto resDto = checkAndSendInviteCode(hboneNo, cmd, loginInfo.getUser().getCustName());
            // 发送成功，清除发送失败次数
            attackJudgerService.clearSendErrorNum(hboneNo);

            write(resDto, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
        } catch (BizException e) {
            if (!BizErrorEnum.SYSTEM_ERROR.getCode().equals(e.getCode())) {
                // 记录失败次数
                attackJudgerService.addSendErrorNum(hboneNo);
            }
            throw e;
        }
    }

    /**
     * 校验并发送邀请码
     * @param hboneNo
     * @param cmd
     * @param custName
     * @return com.howbuy.cgi.trade.simu.model.dto.relatedaccount.SendInviteCodeDto
     * @author: huaqiang.liu
     * @date: 2021/7/20 11:41
     * @since JDK 1.8
     */
    private SendInviteCodeDto checkAndSendInviteCode(String hboneNo, SendInviteCodeCmd cmd, String custName) {
        // 校验账户信息
        String inviteesHboneNo = relatedAccountService.queryHboneNoByMobile(cmd.getMobile());
        if (StringUtils.isBlank(inviteesHboneNo)) {
            LOG.error("未查询到一账通号");
            throw new BizException(BizErrorEnum.INVITE_FAIL.getCode(), ERROR_CAN_NOT_INVITED);
        }
        String inviteId;
        // 查询邀请码
        QueryInvitesModel model = new QueryInvitesModel();
        model.setSubHboneNo(inviteesHboneNo);
        model.setUsed(false);
        List<RelatedAccountInviteBean> inviteBeans = inviteService.queryInvitesEffective(model);

        Date limitDate;
        // 不存在可用的邀请关系
        if (CollectionUtils.isEmpty(inviteBeans)) {
            // 校验被邀请人投顾信息
            String inviteesConsCode = checkConsInfo(inviteesHboneNo);
            // 校验是否可邀请
            checkForInvite(cmd.getAccountId(), inviteesHboneNo, cmd.getRole());
            // 校验是否同一投顾
            checkSameCons(hboneNo, inviteesConsCode);
            // 生成邀请码
            GenerateInviteCodeResponse genRes = inviteService.generateInviteCode(cmd.getAccountId(), inviteesHboneNo, cmd.getRole());
            inviteId = genRes.getInviteId();
            limitDate = DateUtils.addMinutes(genRes.getInviteDate(), Integer.parseInt(genRes.getPeriodOfValidity()));
        } else {
            // 已存在邀请关系
            for (RelatedAccountInviteBean bean : inviteBeans) {
                if (!cmd.getAccountId().equals(bean.getRelatedAccountId())) {
                    log.error("被邀请人已被其他人邀请 inviteInfo:{}", JSON.toJSONString(bean));
                    throw new BizException(BizErrorEnum.INVITE_FAIL.getCode(), ERROR_CAN_NOT_INVITED);
                }
            }
            if (YesOrNoEnum.YES.getCode().equals(cmd.getResendFlag())) {
                // 验证重发间隔
                attackJudgerService.checkSendInterval(hboneNo, inviteesHboneNo);
                // 重发
                RelatedAccountInviteBean bean = inviteBeans.get(0);
                inviteId = bean.getInviteId();
                limitDate = DateUtils.addMinutes(bean.getInviteDate(), Integer.parseInt(bean.getPeriodOfValidity()));
            } else {
                // 不能重复邀请
                throw new BizException(BizErrorEnum.INVITE_FAIL.getCode(), ERROR_REINVITE);
            }
        }
        // 发送邀请链接
        sendInviteUrl(inviteId, inviteesHboneNo, custName);
        // 设置重发限制
        attackJudgerService.addSendInterval(hboneNo, inviteesHboneNo);
        // 返回
        SendInviteCodeDto resDto = new SendInviteCodeDto();
        resDto.setInviteId(inviteId);
        resDto.setTimeLimit(DateUtils.formatToString(limitDate, DateUtils.YYYYMMDDHHMMSS));
        return resDto;
    }

    /**
     * 校验是否可邀请
     * @param accountId
     * @param subHboneNo
     * @param role
     * @return void
     * @author: huaqiang.liu
     * @date: 2021/7/20 10:40
     * @since JDK 1.8
     */
    private void checkForInvite(String accountId, String subHboneNo, String role) {
        // 校验被邀请人信息
        String resCode = inviteService.checkForInvite(accountId, subHboneNo, role);
        if (RemoteUtil.isSuccess(resCode)) {
            return;
        }
        // 不可被邀请
        // 5220023-客户信息不存在       // 子账户
        // 5220024-客户信息状态不为正常
        // 5220173-副账户投资者类型不正确
        // 5220014-未找到有效的分销交易账户
        if (RelatedAccoutUtils.equalsAny(resCode, "5220023", "5220024", "5220173", "5220014")) {
            throw new BizException(BizErrorEnum.INVITE_FAIL.getCode(), ERROR_CAN_NOT_INVITED);
        }
        // 信息不全
        // 5220168-客户密文信息不存在    // 子账户
        // 5220172-性别不能为空
        if (RelatedAccoutUtils.equalsAny(resCode, "5220168", "5220172")) {
            throw new BizException(BizErrorEnum.INVITE_FAIL.getCode(), ERROR_LOSE_INFO);
        }
        // 年龄不符
        // 5220178-主账户和副账户的年龄差过大或过小/主副账户年龄过小，不能添加爱人
        if (RelatedAccoutUtils.equalsAny(resCode, "5220178")) {
            throw new BizException(BizErrorEnum.INVITE_FAIL.getCode(), ERROR_AGE_NOT_MATCH);
        }
        // 性别不符
        // 5220182-性别错误/同性别不能是爱人关系
        if (RelatedAccoutUtils.equalsAny(resCode, "5220182")) {
            throw new BizException(BizErrorEnum.INVITE_FAIL.getCode(), ERROR_SEX_NOT_MATCH);
        }
        // 已存在关联账户
        // 5220181-已是主账户,则不能创建子关联账户
        // 5220179-已是子账户,则不能创建子关联账户
        if (RelatedAccoutUtils.equalsAny(resCode, "5220181", "5220179")) {
            throw new BizException(BizErrorEnum.INVITE_FAIL.getCode(), ERROR_EXISTS);
        }
        // 其它异常
        throw new BizException(BizErrorEnum.SYSTEM_ERROR.getCode(), BizErrorEnum.SYSTEM_ERROR.getDesc());
    }

    /**
     * 检查投顾信息
     * @param inviteesHboneNo
     * @return String
     * @author: huaqiang.liu
     * @date: 2021/7/15 20:58
     * @since JDK 1.8
     */
    private String checkConsInfo(String inviteesHboneNo) {
        // 校验被邀请人投顾信息
        ConscustInfoDomain inviteesConscustInfo = relatedAccountService.queryConscustInfo(inviteesHboneNo);
        if (inviteesConscustInfo==null) {
            LOG.error("未查到被邀请人投顾信息");
            throw new BizException(BizErrorEnum.INVITE_FAIL.getCode(), ERROR_CAN_NOT_INVITED);
        }
        String ishighorg = inviteesConscustInfo.getIshighorg();
        if (!YesOrNoEnum.YES.getCode().equals(ishighorg)) {
            LOG.error("被邀请人所属投顾非IC/HBC下");
            throw new BizException(BizErrorEnum.INVITE_FAIL.getCode(), ERROR_CAN_NOT_INVITED);
        }
        return inviteesConscustInfo.getConscode();
    }

    /**
     * 检查是否同一投顾
     * @param inviterHboneNo
     * @param inviteesConsCode
     * @return void
     * @author: huaqiang.liu
     * @date: 2021/7/28 16:32
     * @since JDK 1.8
     */
    private void checkSameCons(String inviterHboneNo, String inviteesConsCode) {
        // 查询邀请人投顾信息
        ConscustInfoDomain conscustInfo = relatedAccountService.queryConscustInfo(inviterHboneNo);
        if (conscustInfo==null) {
            LOG.error("未查到邀请人投顾");
            throw new BizException(BizErrorEnum.SYSTEM_ERROR.getCode(), BizErrorEnum.SYSTEM_ERROR.getDesc());
        }
        // 非同一投顾
        if (!conscustInfo.getConscode().equals(inviteesConsCode)) {
            LOG.error("邀请人与被邀请人非同一投顾 邀请人投顾：{} 被邀请人投顾：{}", conscustInfo.getConscode(), inviteesConsCode);
            throw new BizException(BizErrorEnum.INVITE_FAIL.getCode(), ERROR_NOT_SAME_CONS);
        }
    }

    /**
     * 发送短链接
     * @param inviteId
     * @param hboneNo
     * @return void
     * @author: huaqiang.liu
     * @date: 2021/7/19 13:55
     * @since JDK 1.8
     */
    private void sendInviteUrl(String inviteId, String hboneNo, String custName) {
        // 生成链接
        String url = simuCcmsServiceRegister.getInviteCodeUrl() + "?inviteId=" + inviteId;
        // 转成短链接
        Date urlEndTime = DateUtils.addMonthOfYear(new Date(), 12);
        String shortLink = messageService.getShortLink(url, urlEndTime);
        if (StringUtils.isBlank(shortLink)) {
            LOG.error("生成短链接失败");
            throw new BizException(BizErrorEnum.SYSTEM_ERROR.getCode(), BizErrorEnum.SYSTEM_ERROR.getDesc());
        }
        // 发送链接
        Map<String, Object> params = new HashMap<>(2);
        params.put("custName", custName);
        params.put("URL", shortLink);
        messageService.sendByHboneNo("60372", hboneNo, params);
    }
    /**
     * @api {post}  /simu/relatedaccount/useinvitecode.htm 使用邀请码
     * @apiGroup RELATED-ACCOUNT
     * @APIName /simu/relatedaccount/useinvitecode.htm
     * @apiDescription 使用邀请码
     *
     * @apiParam {String} inviteId 邀请id
     * @apiParam {String} inviteCode 邀请码
     *
     * @apiSuccess {String} desc 返回描述
     * @apiSuccess {String} code 返回码
     *                              H008-失败次数过多（desc为重试冷静期）
     *                              H009-邀请码错误
     *                              H010-邀请码已过期
     * @apiSuccess {Object} body 数据
     *
     */
    @RequestMapping("/simu/relatedaccount/useinvitecode.htm")
    public void useInviteCode(HttpServletRequest request, HttpServletResponse response) throws Exception {
        UseInviteCodeCmd cmd = getCommand(UseInviteCodeCmd.class);
        ValidateUtil.assertValid(cmd);
        TradeSession loginInfo = getCustSession();
        String hboneNo = loginInfo.getUser().getHboneNo();

        // 校验是否锁定
        attackJudgerService.checkUseIsLock(hboneNo);

        try {
            // 使用邀请码
            String inviteesHboneNo = inviteService.useInviteCode(cmd.getInviteId(), cmd.getInviteCode());

            // 使用成功，清除使用失败次数
            attackJudgerService.clearUseErrorNum(hboneNo);

            // 发送消息
            SendAddFamilyMemberMsgTask sendAddFamilyMemberMsgTask = new SendAddFamilyMemberMsgTask();
            sendAddFamilyMemberMsgTask.setCustName(loginInfo.getUser().getCustName());
            sendAddFamilyMemberMsgTask.setHboneService(hboneService);
            sendAddFamilyMemberMsgTask.setInviteesHboneNo(inviteesHboneNo);
            sendAddFamilyMemberMsgTask.setHboneNo(hboneNo);
            sendAddFamilyMemberMsgTask.setMessageService(messageService);
            howBuyRunTaskUil.runTask(sendAddFamilyMemberMsgTask);
            write("成功", CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
        } catch (BizException e) {
            if (!BizErrorEnum.SYSTEM_ERROR.getCode().equals(e.getCode())) {
                // 记录失败次数
                attackJudgerService.addUseErrorNum(hboneNo);
            }
            throw e;
        }
    }


    /**
     * @api {post}  /simu/relatedaccount/queryinvitedtl.htm 查询邀请详情
     * @apiGroup RELATED-ACCOUNT
     * @APIName /simu/relatedaccount/queryinvitedtl.htm
     * @apiDescription 查询邀请详情
     *
     * @apiParam {String} accountId 家庭关联账户id
     * @apiParam {String} inviteId 邀请id
     *
     * @apiSuccess {String} desc 返回描述
     * @apiSuccess {String} code 返回码
     * @apiSuccess {Object} body 数据
     * @apiSuccess (body) {String} inviteId 邀请id
     * @apiSuccess (body) {String} role 角色 1-父亲;2-母亲;3-爱人;4-爱人父;5-爱人母;6-儿子;7-女儿
     * @apiSuccess (body) {String} mobile 被邀请人手机
     * @apiSuccess (body) {String} timeLimit 到期时间yyyyMMddHHmmss
     *
     */
    @RequestMapping("/simu/relatedaccount/queryinvitedtl.htm")
    public void queryInviteDtl(HttpServletRequest request, HttpServletResponse response) throws Exception {
        QueryInviteDtlCmd cmd = getCommand(QueryInviteDtlCmd.class);
        ValidateUtil.assertValid(cmd);

        InviteDtlDto resDto = new InviteDtlDto();

        // 查询邀请详情
        QueryInvitesModel model = new QueryInvitesModel();
        model.setAccountId(cmd.getAccountId());
        model.setInviteId(cmd.getInviteId());
        List<RelatedAccountInviteBean> inviteBeans = inviteService.queryInvites(model);
        if (CollectionUtils.isEmpty(inviteBeans)) {
            throw new BizException(BizErrorEnum.SYSTEM_ERROR.getCode(), BizErrorEnum.SYSTEM_ERROR.getDesc());
        }

        // 邀请详情
        RelatedAccountInviteBean inviteBean = inviteBeans.get(0);
        resDto.setInviteId(inviteBean.getInviteId());
        resDto.setRole(inviteBean.getRelatedAccountRole().getValue());
        Date limitDate = DateUtils.addMinutes(inviteBean.getInviteDate(), Integer.parseInt(inviteBean.getPeriodOfValidity()));
        resDto.setTimeLimit(DateUtils.formatToString(limitDate, DateUtils.YYYYMMDDHHMMSS));

        // 查询手机明文
        CustMobileModel custMobileModel = accPlaintextService.queryCustMobile(null, inviteBean.getSubHboneNo());
        resDto.setMobile(custMobileModel.getMobile());

        write(resDto, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }

    /**
     * @api {post}  /simu/relatedaccount/queryinvitecode.htm 查询邀请码
     * @apiGroup RELATED-ACCOUNT
     * @APIName /simu/relatedaccount/queryinvitecode.htm
     * @apiDescription 查询邀请码
     *
     * @apiParam {String} inviteId 邀请id
     *
     * @apiSuccess {String} desc 返回描述
     * @apiSuccess {String} code 返回码 H011-客户身份不匹配
     * @apiSuccess {Object} body 数据
     * @apiSuccess (body) {String} inviteCode 邀请码
     * @apiSuccess (body) {String} inviter 邀请人姓名
     * @apiSuccess (body) {String} expireStatus 邀请码过期状态 0未过期1已过期
     * @apiSuccess (body) {String} signStatus 协议签署状态（未签时不返回邀请码）0未签1已签
     *
     */
    @RequestMapping("/simu/relatedaccount/queryinvitecode.htm")
    public void queryInviteCode(HttpServletRequest request, HttpServletResponse response) throws Exception {
        TradeSession loginInfo = this.getCustSession();
        if (null == loginInfo || null == loginInfo.getUser()) {
            LOG.info("loginInfo is null!");
            throw new BizException(BizErrorEnum.NEED_RELOGIN.getCode(), BizErrorEnum.NEED_RELOGIN.getDesc());
        }
        // 邀请id
        String inviteId = getString("inviteId");
        if (StringUtils.isBlank(inviteId)) {
            throw new BizException(BizErrorEnum.REQUEST_PARAMS_ERROR.getCode(), BizErrorEnum.REQUEST_PARAMS_ERROR.getDesc());
        }

        InviteCodeDto resDto = new InviteCodeDto();

        // 查询邀请详情
        QueryInvitesModel model = new QueryInvitesModel();
        model.setInviteId(inviteId);
        List<RelatedAccountInviteBean> inviteBeans = inviteService.queryInvites(model);
        if (CollectionUtils.isEmpty(inviteBeans)) {
            throw new BizException(BizErrorEnum.SYSTEM_ERROR.getCode(), BizErrorEnum.SYSTEM_ERROR.getDesc());
        }

        // 邀请详情
        RelatedAccountInviteBean inviteBean = inviteBeans.get(0);
        if (!loginInfo.getUser().getHboneNo().equals(inviteBean.getSubHboneNo())) {
            // 客户身份不匹配
            throw new BizException(BizErrorEnum.USER_NOT_MATCH.getCode(), BizErrorEnum.USER_NOT_MATCH.getDesc());
        }

        // 查询邀请人一账通
        RelatedAccountBean account = relatedAccountService.queryFamilyRelatedAccount(null, inviteBean.getRelatedAccountId());
        if (account != null) {
            // 查询邀请人姓名
            MemberCustInfoModel custInfo = hboneService.queryCustInfoByHboneNo(account.getHboneNo());
            if (custInfo != null) {
                resDto.setInviter(custInfo.getCustName());
            }
        }

        if (inviteBean.getAgreementSignDate() == null) {
            // 未签协议不返回信息
            resDto.setSignStatus(YesOrNoEnum.NO.getCode());
        } else {
            resDto.setSignStatus(YesOrNoEnum.YES.getCode());
            resDto.setInviteCode(inviteBean.getInviteCode());
            // 处理过期状态
            Date limitDate = DateUtils.addMinutes(inviteBean.getInviteDate(), Integer.parseInt(inviteBean.getPeriodOfValidity()));
            if (limitDate.compareTo(new Date()) <= 0) {
                resDto.setExpireStatus(YesOrNoEnum.YES.getCode());
            } else {
                resDto.setExpireStatus(YesOrNoEnum.NO.getCode());
            }
        }

        write(resDto, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }

    /**
     * @api {post}  /simu/relatedaccount/signagreement.htm 签署协议
     * @apiGroup RELATED-ACCOUNT
     * @APIName /simu/relatedaccount/signagreement.htm
     * @apiDescription 签署协议
     *
     * @apiParam {String} inviteId 邀请id
     *
     * @apiSuccess {String} desc 返回描述
     * @apiSuccess {String} code 返回码
     * @apiSuccess {Object} body 数据
     *
     */
    @RequestMapping("/simu/relatedaccount/signagreement.htm")
    public void signAgreement(HttpServletRequest request, HttpServletResponse response) throws Exception {
        TradeSession loginInfo = this.getCustSession();
        if (null == loginInfo || null == loginInfo.getUser()) {
            LOG.info("loginInfo is null!");
            throw new BizException(BizErrorEnum.NEED_RELOGIN.getCode(), BizErrorEnum.NEED_RELOGIN.getDesc());
        }
        // 邀请id
        String inviteId = getString("inviteId");
        if (StringUtils.isBlank(inviteId)) {
            throw new BizException(BizErrorEnum.REQUEST_PARAMS_ERROR.getCode(), BizErrorEnum.REQUEST_PARAMS_ERROR.getDesc());
        }

        inviteService.signAgreement(loginInfo.getUser().getHboneNo(), inviteId);

        write("成功", CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }
}