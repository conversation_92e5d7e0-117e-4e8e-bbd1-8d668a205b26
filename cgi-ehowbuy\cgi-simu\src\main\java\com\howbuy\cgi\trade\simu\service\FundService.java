package com.howbuy.cgi.trade.simu.service;

import com.howbuy.cgi.trade.ccms.simu.SimuCcmsServiceRegister;
import com.howbuy.interlayer.product.model.HighProductBaseInfoModel;
import com.howbuy.interlayer.product.model.HighProductDBInfoModel;
import com.howbuy.interlayer.product.model.HighProductInfoModel;
import com.howbuy.interlayer.product.model.JjxswConfigModel;
import com.howbuy.interlayer.product.service.HighProductService;
import com.howbuy.simu.service.base.product.SmxxService;
import com.howbuy.tms.common.enums.busi.HighProductAgreementValueEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @Description:产品相关类
 * @Author: yun.lu
 * Date: 2023/9/19 17:12
 */
@Service("fundService")
@Slf4j
public class FundService {
    @Autowired
    @Qualifier("simu.smxxService")
    private SmxxService smxxService;

    @Autowired
    @Qualifier("simu.highProductService")
    private HighProductService highProductService;

    @Autowired
    private SimuCcmsServiceRegister simuCcmsServiceRegister;

    /**
     * db的净值map
     */
    public Map<String, Integer> queryNavPrecisionByFundCode(List<String> fundCodeList) {
        if (CollectionUtils.isEmpty(fundCodeList)) {
            return new HashMap<>(1);
        }
        List<JjxswConfigModel> jjxswConfigModels = highProductService.queryJjxswConfigByJjdm(fundCodeList);
        if (CollectionUtils.isEmpty(jjxswConfigModels)) {
            return new HashMap<>(1);
        }
        Map<String, Integer> navMap = new HashMap<>(fundCodeList.size());
        for (JjxswConfigModel jjxswConfigModel : jjxswConfigModels) {
            navMap.put(jjxswConfigModel.getJjdm(), jjxswConfigModel.getJzws());
        }
        return navMap;
    }


    /**
     * 判断产品是否是香港产品
     *
     * @param fundCode 产品编码
     * @return 是否香港产品, true-是香港产品, false-不是香港产品
     */
    public boolean isHkProduct(String fundCode) {
        List<HighProductDBInfoModel> highProductInfoList = highProductService.getHighProductDBInfo(Collections.singletonList(fundCode));
        if (CollectionUtils.isEmpty(highProductInfoList)) {
            log.info("根据产品编码查询产品信息为空,就认为不是香港产品,fundCode={}", fundCode);
            return false;
        }
        String hkSaleFlag = highProductInfoList.get(0).getHkSaleFlag();
        if (StringUtils.isBlank(hkSaleFlag)) {
            log.info("根据产品编码查询产品信息hkSaleFlag为空,就认为不是香港产品,fundCode={}", fundCode);
            return false;
        }
        return YesOrNoEnum.YES.getCode().equals(hkSaleFlag);
    }


    public String getFundPlAuth(String fundCode) {
        log.info("查询产品披露权限,fundCode={}", fundCode);
        Map<String, String> authMap = smxxService.getPlCpflMap(Collections.singletonList(fundCode));
        log.info("查询产品披露权限-非交易结果,authMap={}", authMap);
        return MapUtils.isEmpty(authMap) ? YesOrNoEnum.NO.getCode() : authMap.containsKey(fundCode) ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode();
    }

    public List<String> getHighRiskConfirmContent(String fundCode) {
        HighProductInfoModel highProductInfo = highProductService.getHighProductInfo(fundCode);
        if (!CollectionUtils.isEmpty(highProductInfo.getAgreementConfList())) {
            for (HighProductInfoModel.AgreementConf agreementConf : highProductInfo.getAgreementConfList()) {
                if (HighProductAgreementValueEnum.HIGH_RISK_CONFIRM.getCode().equals(agreementConf.getAgreementCode()) && StringUtils.isNotBlank(agreementConf.getNoticeTemplate())) {
                    return parseContentList(agreementConf.getNoticeTemplate());
                }
            }
        }
        return parseContentList(simuCcmsServiceRegister.getHighRiskConfirmDefaultContent());
    }

    private List<String> parseContentList(String contents) {
        if (StringUtils.isBlank(contents)) {
            return new ArrayList<>();
        }
        contents = contents.replaceAll("huanHang", "\n");
        String[] contentList = contents.split("\n");
        List<String> newContentList = new ArrayList<>();
        for (String content : contentList) {
            if (StringUtils.isNotBlank(content)) {
                newContentList.add(content.trim());
            }
        }
        return newContentList;
    }

    public HighProductBaseInfoModel queryFundInfoByFundCode(String fundCode) {
        return highProductService.getHighProductBaseInfo(fundCode);
    }
}
