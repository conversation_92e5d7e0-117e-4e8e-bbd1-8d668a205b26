package com.howbuy.cgi.trade.simu.common.config;

import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.session.web.http.SessionRepositoryFilter;
import org.springframework.web.filter.CharacterEncodingFilter;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: xin.jiang.cn
 * @date: 2023年09月13日 17:33:16
 * @description:
 */
@Configuration
public class FilterConfig {


    /**
     * 配置CharacterEncodingFilter确保所有HTTP请求都使用UTF-8编码。
     * 这有助于防止编码相关的安全问题，并确保数据的一致性。
     * <p>
     * 注意：为了提高可维护性和扩展性，以及潜在的异常处理能力，建议将过滤器配置和管理
     * 统一到一个专门的配置类或服务中。这里只是单一功能的示例。
     *
     * @return FilterRegistrationBean<CharacterEncodingFilter> 配置好的注册Bean
     */
    @Bean(name = "customCharacterEncodingFilter")
    public FilterRegistrationBean<CharacterEncodingFilter> characterEncodingFilter() {
        FilterRegistrationBean<CharacterEncodingFilter> registrationBean = new FilterRegistrationBean<>();
        CharacterEncodingFilter filter = new CharacterEncodingFilter();

        // 将编码方式设置为UTF-8。为了提高灵活性，建议在应用的配置文件中定义此值，
        filter.setEncoding("UTF-8");

        registrationBean.setFilter(filter);
        // 将过滤器应用到所有URL模式。在特定情况下，可能需要根据实际需求调整此模式。
        registrationBean.addUrlPatterns("/*");

        // 异常处理：尽管CharacterEncodingFilter通常不会抛出异常，但在更复杂的过滤器或配置中，
        // 建议添加适当的异常处理逻辑，例如使用try-catch语句或定义错误处理过滤器。

        return registrationBean;
    }


    @Bean
    public FilterRegistrationBean<SessionRepositoryFilter> sessionRepositoryFilter() {
        SessionRepositoryFilter filter = new SessionRepositoryFilter<>();
        Map<String, String> initParameters = new HashMap<>();
        initParameters.put("howbuySessionPrefix", "ehowbuy:trade:session");
        initParameters.put("howbuyCookiePrefix", "EHB");
        FilterRegistrationBean<SessionRepositoryFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setInitParameters(initParameters);
        registrationBean.setFilter(filter);
        registrationBean.addUrlPatterns("/*");
        registrationBean.setOrder(-1);

        return registrationBean;
    }
}
