<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.howbuy.interlayer</groupId>

    <artifactId>product-center-client</artifactId>
    <packaging>jar</packaging>
    <version>4.8.91-RELEASE</version>
    <name>product-center-client</name>

    <properties>
	 	<com.howbuy.product-center-model.version>4.8.91-RELEASE</com.howbuy.product-center-model.version>
	</properties>

    <dependencies>
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.6</version>
        </dependency>
        <dependency>
            <groupId>com.howbuy.interlayer</groupId>
            <artifactId>product-center-model</artifactId>
            <version>${com.howbuy.product-center-model.version}</version>
        </dependency>

        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>utils</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>2.4</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.5</version>
                <configuration>
					<encoding>UTF-8</encoding>
					<includes>
						<include>**/*TestM.java</include>
					</includes>
				</configuration>
<!--                <configuration>-->
<!--                    <skipTests>true</skipTests>-->
<!--                </configuration>-->
            </plugin>
            <plugin>
	            <groupId>org.apache.maven.plugins</groupId>
	            <artifactId>maven-jar-plugin</artifactId>
	            <configuration>
	                <archive>
	                    <manifestEntries>
	                        <Package-Stamp>${parelease}</Package-Stamp>
	                    </manifestEntries>
	                </archive>
	            </configuration>
	        </plugin>
        </plugins>
    </build>
    <distributionManagement>
        <repository>
            <id>howbuy-release</id>
            <name>howbuy-release</name>
            <url>http://nx-java.howbuy.pa/repository/howbuy-release/</url>
        </repository>
        <snapshotRepository>
            <id>howbuy-snapshot</id>
            <name>howbuy-snapshot</name>
            <url>http://nx-java.howbuy.pa/repository/howbuy-snapshot/</url>
        </snapshotRepository>
    </distributionManagement>
</project>