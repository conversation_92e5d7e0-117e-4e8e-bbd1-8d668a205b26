package com.howbuy.cgi.trade.simu.interceptor;

import com.howbuy.cgi.common.util.CgiParamUtil;
import com.howbuy.cgi.trade.simu.service.relatedaccount.AccCenterService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
@Service("custRiskTestInterceptor")
public class CustRiskTestInterceptor extends AbstractStatusInterceptor {
    private static final Logger LOG = LoggerFactory.getLogger(CustRiskTestInterceptor.class);
    private static final String SIMU_KYC_INFO = "SIMU_KYC_INFO_NEW"; //私募用户的kyc信息;
    @Autowired
    private AccCenterService accCenterService;

    private final static String JgCorpId = "000007";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        if (isExcludes(request.getRequestURI())) {
            return true;
        }

        String corpId = CgiParamUtil.getCorpId();
        if (StringUtils.isNotEmpty(corpId)) {
            if (JgCorpId.equals(corpId)) {
                LOG.info("机构渠道不校验kyc");
                return true;
            }
        }

        checkKyc(request, accCenterService);
        return true;
    }
}
