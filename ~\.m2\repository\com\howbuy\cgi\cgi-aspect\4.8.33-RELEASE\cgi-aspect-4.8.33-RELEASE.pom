<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.howbuy.cgi</groupId>
		<artifactId>cgi-ehowbuy-common-parent</artifactId>
		<version>4.8.33-RELEASE</version>
		<relativePath>../pom.xml</relativePath>
	</parent>

	<name>cgi-aspect</name>
	<artifactId>cgi-aspect</artifactId>
	<version>4.8.33-RELEASE</version>


	<dependencies>
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
		</dependency>
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>gateway-captcha-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.howbuy.cgi</groupId>
			<artifactId>cgi-common</artifactId>
		</dependency>
		<dependency>
			<groupId>com.howbuy.acccenter</groupId>
			<artifactId>acc-center-facade</artifactId>
		</dependency>
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>howbuy-cms-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>howbuy-wireless-entity</artifactId>
		</dependency>
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>howbuy-session</artifactId>
		</dependency>
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>howbuy-cachemanagement</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-webmvc</artifactId>
		</dependency>
		<dependency>
			<groupId>net.sf.oval</groupId>
			<artifactId>oval</artifactId>
		</dependency>
		<dependency>
			<groupId>com.googlecode.protobuf-java-format</groupId>
			<artifactId>protobuf-java-format</artifactId>
		</dependency>
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.github.sgroschupf</groupId>
			<artifactId>zkclient</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.dubbo</groupId>
			<artifactId>dubbo</artifactId>
		</dependency>
		<dependency>
			<groupId>com.howbuy.cc</groupId>
			<artifactId>center-client</artifactId>
		</dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-context</artifactId>
        </dependency>
		<dependency>
			<groupId>com.howbuy.cgi</groupId>
			<artifactId>service-common</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot</artifactId>
		</dependency>
	</dependencies>
</project>